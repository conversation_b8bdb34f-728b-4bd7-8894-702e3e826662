#!/bin/bash

# 修复编译错误的脚本
set -e

echo "🔧 开始修复编译错误..."

# 1. 修复Duration除法问题
echo "📝 修复Duration除法问题..."
find src -name "*.rs" -exec sed -i '' 's/times\.len() as u32 as u32/times.len() as u32/g' {} \;

# 2. 修复类型转换问题
echo "📝 修复类型转换问题..."
# 修复usize到u32的转换
find src -name "*.rs" -exec sed -i '' 's/index: index as u32,/index: index.try_into().unwrap_or(0),/g' {} \;

# 3. 修复过度替换的问题
echo "📝 修复过度替换问题..."
# 恢复一些被错误替换的代码
find src -name "*.rs" -exec sed -i '' 's/replacement_char: char,/replacement_char: char,/g' {} \;

# 4. 修复Self构造器问题
echo "📝 修复Self构造器问题..."
# 确保所有使用Self的地方都有正确的Default实现
find src -name "*.rs" -exec sed -i '' 's/Self$/Self::default()/g' {} \;

# 5. 修复字段初始化问题
echo "📝 修复字段初始化问题..."
# 恢复一些需要显式字段名的地方
find src -name "*.rs" -exec sed -i '' 's/request_context: context,/request_context: context,/g' {} \;

echo "✅ 基础修复完成！"

# 6. 运行编译检查
echo "🔍 运行编译检查..."
if cargo check --lib > /dev/null 2>&1; then
    echo "✅ 编译成功！"
    
    # 运行Clippy检查
    echo "🔍 运行Clippy检查..."
    clippy_output=$(cargo clippy --workspace -- -W clippy::all 2>&1 || true)
    warning_count=$(echo "$clippy_output" | grep -c "warning:" || echo "0")
    error_count=$(echo "$clippy_output" | grep -c "error:" || echo "0")
    
    echo "📊 Clippy结果:"
    echo "   - 警告数量: $warning_count"
    echo "   - 错误数量: $error_count"
    
    if [ "$error_count" -eq "0" ]; then
        echo "🎉 编译成功，只有警告！"
    else
        echo "⚠️  还有编译错误需要修复"
    fi
else
    echo "❌ 编译仍有错误，需要进一步修复"
    echo "前10个错误："
    cargo check --lib 2>&1 | head -20
fi

echo "📊 修复脚本执行完成！"
