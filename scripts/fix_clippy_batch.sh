#!/bin/bash

# 批量修复Clippy警告的脚本
set -e

echo "🚀 开始批量修复Clippy警告..."

# 修复冗余闭包 - Database错误
echo "📝 修复Database错误处理的冗余闭包..."
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Database(e))/\.map_err(GatewayError::Database)/g' {} \;

# 修复冗余闭包 - Redis错误
echo "📝 修复Redis错误处理的冗余闭包..."
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Redis(e))/\.map_err(GatewayError::Redis)/g' {} \;

# 修复冗余闭包 - Serialization错误
echo "📝 修复Serialization错误处理的冗余闭包..."
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Serialization(e))/\.map_err(GatewayError::Serialization)/g' {} \;

# 修复不必要的.into()调用
echo "📝 修复不必要的.into()调用..."
find src -name "*.rs" -exec sed -i '' 's/GatewayError::Database(e\.into())/GatewayError::Database(e)/g' {} \;

echo "✅ 批量修复完成！"
echo "🔍 运行Clippy检查剩余问题..."

# 运行clippy检查
cargo clippy --workspace -- -D warnings 2>&1 | head -50 || true

echo "📊 修复脚本执行完成！"
