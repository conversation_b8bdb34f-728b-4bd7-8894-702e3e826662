#!/bin/bash

# 保守的Clippy修复脚本 - 只修复安全的警告
set -e

echo "🔧 开始保守的Clippy修复..."

# 1. 只修复明确安全的冗余闭包
echo "📝 修复安全的冗余闭包..."
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Database(e))/\.map_err(GatewayError::Database)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Redis(e))/\.map_err(GatewayError::Redis)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Jwt(e))/\.map_err(GatewayError::Jwt)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Serialization(e))/\.map_err(GatewayError::Serialization)/g' {} \;

# 2. 修复明确的手动实现
echo "📝 修复手动字符串剥离..."
# 这个需要手动处理，因为涉及具体的逻辑

# 3. 移除不必要的引用（只在安全的地方）
echo "📝 移除明确不必要的引用..."
find src -name "*.rs" -exec sed -i '' 's/&provider\.name()/provider.name()/g' {} \;

# 4. 修复Range::contains
echo "📝 修复Range::contains..."
find src -name "*.rs" -exec sed -i '' 's/status_code >= 200 && status_code < 300/(200..300).contains(\&status_code)/g' {} \;

echo "✅ 保守修复完成！"

# 5. 检查结果
echo "🔍 检查修复结果..."
echo "运行Clippy检查（只显示警告）..."
cargo clippy --workspace 2>&1 | grep "warning:" | wc -l | xargs echo "警告数量:"

echo "📊 保守修复脚本执行完成！"
echo "💡 建议: 先解决编译错误，再处理Clippy警告"
