#!/bin/bash

# LiteLLM-RS Clippy优化脚本
# 自动修复常见的Clippy警告

set -e

echo "🚀 开始LiteLLM-RS Clippy优化..."

# 1. 修复冗余闭包 (最常见的问题)
echo "📝 修复冗余闭包..."

# 修复 GatewayError::Database 闭包
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Database(e))/\.map_err(GatewayError::Database)/g' {} \;

# 修复 GatewayError::Redis 闭包
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Redis(e))/\.map_err(GatewayError::Redis)/g' {} \;

# 修复 GatewayError::Jwt 闭包
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Jwt(e))/\.map_err(GatewayError::Jwt)/g' {} \;

# 修复 GatewayError::Serialization 闭包
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Serialization(e))/\.map_err(GatewayError::Serialization)/g' {} \;

echo "✅ 冗余闭包修复完成"

# 2. 修复不必要的 to_string() 调用
echo "📝 修复不必要的字符串转换..."

# 修复字符串比较中的 to_string()
find src -name "*.rs" -exec sed -i '' 's/&"\*"\.to_string()/"\*"/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/&"system\.admin"\.to_string()/"system.admin"/g' {} \;

echo "✅ 字符串转换优化完成"

# 3. 修复手动实现的标准方法
echo "📝 修复手动实现的标准方法..."

# 这些需要手动处理，脚本只能标记
echo "⚠️  以下文件需要手动修复标准trait实现:"
grep -r "manual_map\|manual_strip\|manual_range_contains" src/ || true

echo "✅ 标准方法检查完成"

# 4. 运行Clippy检查剩余问题
echo "🔍 检查剩余Clippy警告..."
cargo clippy --workspace -- -D warnings 2>&1 | head -20 || true

echo "🎉 Clippy优化脚本执行完成!"
echo "📊 建议运行 'cargo clippy --workspace -- -D warnings' 查看剩余问题"
