#!/bin/bash

# 最终修复所有Clippy警告的脚本
set -e

echo "🚀 开始最终修复所有Clippy警告..."

# 1. 修复类型转换问题
echo "📝 修复类型转换问题..."

# 修复u64到u32的转换
find src -name "*.rs" -exec sed -i '' 's/requests_today += requests;/requests_today += requests as u32;/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/tokens_today += tokens;/tokens_today += tokens as u32;/g' {} \;

# 2. 修复Duration除法问题
echo "📝 修复Duration除法问题..."
find src -name "*.rs" -exec sed -i '' 's/\.sum::<Duration>() \/ times\.len()/\.sum::<Duration>() \/ times.len() as u32/g' {} \;

# 3. 修复冗余字段名
echo "📝 修复冗余字段名..."
find src -name "*.rs" -exec sed -i '' 's/model: model,/model,/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/data: data,/data,/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/error: error,/error,/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/result: result,/result,/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/response: response,/response,/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/request: request,/request,/g' {} \;

# 4. 修复不必要的引用
echo "📝 修复不必要的引用..."
find src -name "*.rs" -exec sed -i '' 's/&provider\.name()/provider.name()/g' {} \;

echo "✅ 批量修复完成！"

# 5. 运行编译检查
echo "🔍 运行编译检查..."
if cargo check --lib > /dev/null 2>&1; then
    echo "✅ 编译成功！"
    
    # 6. 运行Clippy检查
    echo "🔍 运行最终Clippy检查..."
    clippy_output=$(cargo clippy --workspace -- -D warnings 2>&1 || true)
    error_count=$(echo "$clippy_output" | grep -c "error:" || echo "0")
    
    echo "📊 剩余错误数量: $error_count"
    
    if [ "$error_count" -eq "0" ]; then
        echo "🎉 所有Clippy警告已修复完成！"
        echo "✨ 项目现在完全通过Clippy检查！"
    else
        echo "⚠️  还有 $error_count 个错误需要手动修复："
        echo "$clippy_output" | head -30
    fi
else
    echo "❌ 编译失败，需要手动修复一些问题"
    cargo check --lib 2>&1 | head -20
fi

echo "📊 最终修复脚本执行完成！"
