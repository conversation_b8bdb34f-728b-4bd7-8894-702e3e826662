#!/bin/bash

# 系统性修复Clippy警告的脚本
set -e

echo "🔧 开始系统性修复Clippy警告..."

# 1. 修复冗余闭包 (最多的问题)
echo "📝 修复冗余闭包..."
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Database(e))/\.map_err(GatewayError::Database)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Redis(e))/\.map_err(GatewayError::Redis)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Jwt(e))/\.map_err(GatewayError::Jwt)/g' {} \;
find src -name "*.rs" -exec sed -i '' 's/\.map_err(|e| GatewayError::Serialization(e))/\.map_err(GatewayError::Serialization)/g' {} \;

# 2. 修复不必要的.into()调用
echo "📝 修复不必要的.into()调用..."
find src -name "*.rs" -exec sed -i '' 's/GatewayError::Database(e\.into())/GatewayError::Database(e)/g' {} \;

# 3. 修复不必要的类型转换
echo "📝 修复不必要的类型转换..."
find src -name "*.rs" -exec sed -i '' 's/ as u32 as u32/ as u32/g' {} \;

# 4. 修复unit struct的default调用
echo "📝 修复unit struct的default调用..."
find src -name "*.rs" -exec sed -i '' 's/Self::default()/Self/g' {} \;

echo "✅ 批量修复完成！"

# 5. 运行编译检查
echo "🔍 运行编译检查..."
if cargo check --lib > /dev/null 2>&1; then
    echo "✅ 编译成功！"
    
    # 运行Clippy检查剩余问题
    echo "🔍 检查剩余Clippy警告..."
    clippy_output=$(cargo clippy --workspace -- -D warnings 2>&1 || true)
    error_count=$(echo "$clippy_output" | grep -c "error:" || echo "0")
    
    echo "📊 剩余错误数量: $error_count"
    
    if [ "$error_count" -eq "0" ]; then
        echo "🎉 所有Clippy警告已修复完成！"
    else
        echo "⚠️  还有 $error_count 个错误需要手动修复"
        echo "前20个错误："
        echo "$clippy_output" | head -40
    fi
else
    echo "❌ 编译失败，需要手动修复一些问题"
    cargo check --lib 2>&1 | head -20
fi

echo "📊 系统性修复脚本执行完成！"
