# Contributing to Rust LiteLLM Gateway

Thank you for your interest in contributing to Rust LiteLLM Gateway! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use issue templates** when available
3. **Provide detailed information**:
   - Rust version
   - Operating system
   - Gateway version
   - Steps to reproduce
   - Expected vs actual behavior
   - Relevant logs or error messages

### Suggesting Features

1. **Check the roadmap** in README.md
2. **Open a discussion** before implementing large features
3. **Provide use cases** and rationale
4. **Consider backwards compatibility**

### Code Contributions

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**
4. **Add tests** for new functionality
5. **Update documentation** if needed
6. **Run tests**: `cargo test`
7. **Run linting**: `cargo clippy`
8. **Format code**: `cargo fmt`
9. **Commit changes**: `git commit -m 'Add amazing feature'`
10. **Push to branch**: `git push origin feature/amazing-feature`
11. **Open a Pull Request**

## 🛠️ Development Setup

### Prerequisites

- Rust 1.85 or later
- PostgreSQL 12+
- Redis 6+
- Docker (optional, for development services)

### Local Development

1. **Clone the repository**:
```bash
git clone https://github.com/majiayu000/litellm-rs.git
cd rust-litellm-gateway
```

2. **Install Rust dependencies**:
```bash
cargo build
```

3. **Start development services**:
```bash
# Using Docker Compose
docker-compose -f docker-compose.dev.yml up -d

# Or manually start PostgreSQL and Redis
```

4. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run database migrations**:
```bash
cargo run --bin migrate
```

6. **Start the development server**:
```bash
cargo run -- --config config/dev.yaml
```

### Running Tests

```bash
# Run all tests
cargo test

# Run specific test
cargo test test_name

# Run tests with output
cargo test -- --nocapture

# Run integration tests
cargo test --test integration_tests
```

### Code Quality

```bash
# Format code
cargo fmt

# Run linter
cargo clippy

# Check for security vulnerabilities
cargo audit

# Generate documentation
cargo doc --open
```

## 📝 Code Style

### Rust Guidelines

- Follow [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)
- Use `cargo fmt` for consistent formatting
- Address all `cargo clippy` warnings
- Write comprehensive documentation with examples
- Use meaningful variable and function names
- Prefer explicit error handling over panics

### Code Organization

- Keep modules focused and cohesive
- Use clear module hierarchies
- Separate concerns (business logic, data access, presentation)
- Write unit tests alongside implementation
- Document public APIs thoroughly

### Commit Messages

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions or changes
- `chore`: Maintenance tasks

Examples:
```
feat(auth): add JWT token refresh endpoint
fix(router): handle provider timeout correctly
docs(api): update OpenAI compatibility documentation
```

## 🧪 Testing Guidelines

### Test Structure

- **Unit tests**: Test individual functions and modules
- **Integration tests**: Test component interactions
- **End-to-end tests**: Test complete workflows
- **Performance tests**: Benchmark critical paths

### Test Organization

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_function_name() {
        // Arrange
        let input = setup_test_data();
        
        // Act
        let result = function_under_test(input).await;
        
        // Assert
        assert_eq!(result.unwrap(), expected_value);
    }
}
```

### Mocking

Use `mockall` for mocking external dependencies:

```rust
#[cfg(test)]
use mockall::predicate::*;
#[cfg(test)]
use mockall::mock;

mock! {
    MyTrait {}
    
    #[async_trait]
    impl MyTrait for MyTrait {
        async fn my_method(&self, arg: String) -> Result<String>;
    }
}
```

## 📚 Documentation

### Code Documentation

- Document all public APIs
- Include examples in documentation
- Explain complex algorithms or business logic
- Use `cargo doc` to generate documentation

### User Documentation

- Update relevant documentation in `docs/`
- Include configuration examples
- Provide migration guides for breaking changes
- Update README.md if needed

## 🔄 Pull Request Process

### Before Submitting

- [ ] Code compiles without warnings
- [ ] All tests pass
- [ ] Code is formatted (`cargo fmt`)
- [ ] Linting passes (`cargo clippy`)
- [ ] Documentation is updated
- [ ] CHANGELOG.md is updated (for significant changes)

### PR Description Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing performed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added for new functionality
```

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** in development environment
4. **Approval** from at least one maintainer
5. **Merge** after all requirements met

## 🏷️ Release Process

### Versioning

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backwards compatible)
- **PATCH**: Bug fixes (backwards compatible)

### Release Checklist

- [ ] Update version in `Cargo.toml`
- [ ] Update `CHANGELOG.md`
- [ ] Create release notes
- [ ] Tag release: `git tag v1.0.0`
- [ ] Push tags: `git push --tags`
- [ ] GitHub Actions will handle the rest

## 🎯 Areas for Contribution

### High Priority
- Streaming response implementation
- Additional AI provider integrations
- Performance optimizations
- Security enhancements

### Medium Priority
- Advanced caching strategies
- Monitoring and observability improvements
- Documentation and examples
- Testing coverage improvements

### Good First Issues
- Configuration validation improvements
- Error message enhancements
- Documentation fixes
- Small bug fixes

## 💬 Communication

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code contributions and reviews

## 📄 License

By contributing to Rust LiteLLM Gateway, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- GitHub contributors page

Thank you for contributing to Rust LiteLLM Gateway! 🚀
