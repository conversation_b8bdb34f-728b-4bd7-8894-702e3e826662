# Configuration Guide

This guide covers all configuration options for the Rust LiteLLM Gateway, including examples and best practices.

## 📋 Configuration Overview

The gateway supports multiple configuration sources with the following precedence (highest to lowest):

1. **Command line arguments**
2. **Environment variables**
3. **Configuration files** (YAML, JSON, TOML)
4. **Default values**

## 📁 Configuration Files

### Supported Formats

- **YAML** (recommended): `config/gateway.yaml`
- **JSON**: `config/gateway.json`
- **TOML**: `config/gateway.toml`

### Loading Configuration

```bash
# Specify config file
./gateway --config config/gateway.yaml

# Use default locations (searched in order)
./gateway
# Searches: ./gateway.yaml, ./config/gateway.yaml, /etc/gateway/gateway.yaml
```

## 🔧 Complete Configuration Reference

### Server Configuration

```yaml
server:
  # Server binding
  host: "0.0.0.0"              # Bind address (default: "127.0.0.1")
  port: 8000                   # Port number (default: 8000)
  
  # Performance tuning
  workers: 4                   # Number of worker threads (default: CPU cores)
  max_connections: 1000        # Max concurrent connections (default: 1000)
  keep_alive: 75               # Keep-alive timeout in seconds (default: 75)
  
  # Request handling
  timeout: 30                  # Request timeout in seconds (default: 30)
  max_body_size: 10485760      # Max request body size in bytes (default: 10MB)
  
  # TLS configuration (optional)
  tls:
    enabled: false
    cert_file: "/path/to/cert.pem"
    key_file: "/path/to/key.pem"
    
  # CORS settings
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["*"]
    max_age: 3600
```

### Provider Configuration

```yaml
providers:
  # OpenAI Provider
  - name: "openai-primary"
    provider_type: "openai"
    enabled: true
    weight: 100                # Routing weight (default: 100)
    priority: 1                # Priority level (lower = higher priority)
    
    config:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      organization: "${OPENAI_ORG_ID}"  # Optional
      
    models:
      - "gpt-4"
      - "gpt-4-turbo"
      - "gpt-3.5-turbo"
      
    limits:
      max_requests_per_minute: 1000
      max_tokens_per_minute: 100000
      
    health_check:
      enabled: true
      interval: 30             # Seconds between health checks
      timeout: 10              # Health check timeout
      retries: 3               # Retry attempts
      
  # Anthropic Provider
  - name: "anthropic-primary"
    provider_type: "anthropic"
    enabled: true
    weight: 80
    
    config:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      
    models:
      - "claude-3-opus-********"
      - "claude-3-sonnet-********"
      - "claude-3-haiku-********"
      
  # Azure OpenAI Provider
  - name: "azure-openai"
    provider_type: "azure"
    enabled: true
    
    config:
      api_key: "${AZURE_OPENAI_API_KEY}"
      endpoint: "${AZURE_OPENAI_ENDPOINT}"
      api_version: "2024-02-01"
      
    models:
      - name: "gpt-4"
        deployment_name: "gpt-4-deployment"
      - name: "gpt-35-turbo"
        deployment_name: "gpt-35-turbo-deployment"
        
  # Google AI Provider
  - name: "google-ai"
    provider_type: "google"
    enabled: true
    
    config:
      api_key: "${GOOGLE_AI_API_KEY}"
      base_url: "https://generativelanguage.googleapis.com"
      
    models:
      - "gemini-pro"
      - "gemini-pro-vision"
```

### Router Configuration

```yaml
router:
  # Routing strategy
  strategy:
    type: "least_latency"       # Options: round_robin, least_latency, least_cost, random, weighted, priority, ab_test, custom
  
  # Health monitoring
  health_check_interval: 30     # Seconds between provider health checks
  unhealthy_threshold: 3        # Failed checks before marking unhealthy
  healthy_threshold: 2          # Successful checks before marking healthy
  
  # Retry configuration
  retry_attempts: 3             # Max retry attempts
  retry_delay: 1000             # Initial retry delay in milliseconds
  retry_backoff: 2.0            # Backoff multiplier
  
  # Timeout settings
  provider_timeout: 30          # Provider request timeout in seconds
  total_timeout: 60             # Total request timeout including retries
  
  # Load balancing
  load_balancing:
    algorithm: "weighted_round_robin"
    health_weight: 0.3          # Weight given to health scores
    latency_weight: 0.4         # Weight given to latency scores
    cost_weight: 0.3            # Weight given to cost scores
    
  # Failover settings
  failover:
    enabled: true
    max_failures: 5             # Max failures before circuit breaker opens
    recovery_time: 60           # Seconds before attempting recovery
```

### Authentication Configuration

```yaml
auth:
  # JWT Configuration
  jwt:
    enabled: true
    secret: "${JWT_SECRET}"     # JWT signing secret
    algorithm: "HS256"          # Signing algorithm
    expiration: 3600            # Token expiration in seconds
    refresh_enabled: true       # Enable refresh tokens
    refresh_expiration: 86400   # Refresh token expiration
    
  # API Key Configuration
  api_key:
    enabled: true
    header: "Authorization"     # Header name for API key
    prefix: "Bearer "           # Optional prefix
    
  # Session Configuration
  session:
    enabled: true
    timeout: 1800               # Session timeout in seconds
    cleanup_interval: 300       # Session cleanup interval
    
  # RBAC Configuration
  rbac:
    enabled: true
    default_role: "user"        # Default role for new users
    admin_role: "admin"         # Admin role name
    
    roles:
      - name: "admin"
        permissions: ["*"]      # All permissions
        
      - name: "user"
        permissions:
          - "chat:create"
          - "completion:create"
          - "embedding:create"
          
      - name: "readonly"
        permissions:
          - "model:list"
          - "health:check"
```

### Storage Configuration

```yaml
storage:
  # Database Configuration
  database:
    url: "${DATABASE_URL}"      # PostgreSQL connection string
    max_connections: 10         # Connection pool size
    min_connections: 1          # Minimum connections
    connection_timeout: 30      # Connection timeout in seconds
    idle_timeout: 600           # Idle connection timeout
    max_lifetime: 3600          # Connection max lifetime
    
    # Migration settings
    auto_migrate: true          # Run migrations on startup
    migration_path: "migrations/"
    
  # Redis Configuration
  redis:
    url: "${REDIS_URL}"         # Redis connection string
    max_connections: 10         # Connection pool size
    connection_timeout: 5       # Connection timeout
    command_timeout: 5          # Command timeout
    
    # Cluster configuration (optional)
    cluster:
      enabled: false
      nodes:
        - "redis://node1:6379"
        - "redis://node2:6379"
        - "redis://node3:6379"
        
  # File Storage Configuration
  files:
    backend: "local"            # Options: local, s3, gcs, azure
    
    # Local storage
    local:
      path: "./data/files"
      
    # S3 configuration
    s3:
      bucket: "${S3_BUCKET}"
      region: "${AWS_REGION}"
      access_key: "${AWS_ACCESS_KEY_ID}"
      secret_key: "${AWS_SECRET_ACCESS_KEY}"
      endpoint: "${S3_ENDPOINT}"  # Optional for S3-compatible services
      
  # Vector Database Configuration (optional)
  vector:
    enabled: false
    backend: "qdrant"           # Options: qdrant, pinecone, weaviate
    
    qdrant:
      url: "${QDRANT_URL}"
      api_key: "${QDRANT_API_KEY}"
      collection: "embeddings"
```

### Caching Configuration

```yaml
caching:
  # Memory cache
  memory:
    enabled: true
    max_size: 1000              # Max number of entries
    ttl: 300                    # Time to live in seconds
    
  # Redis cache
  redis:
    enabled: true
    ttl: 3600                   # Default TTL
    key_prefix: "gateway:"      # Key prefix
    
  # Response caching
  response:
    enabled: true
    ttl: 300                    # Response cache TTL
    vary_by:                    # Cache key variations
      - "user_id"
      - "model"
      - "temperature"
      
  # Semantic caching (requires vector database)
  semantic:
    enabled: false
    similarity_threshold: 0.95  # Similarity threshold for cache hits
    embedding_model: "text-embedding-ada-002"
```

### Monitoring Configuration

```yaml
monitoring:
  # Metrics
  metrics:
    enabled: true
    port: 9090                  # Prometheus metrics port
    path: "/metrics"            # Metrics endpoint path
    
  # Health checks
  health:
    enabled: true
    port: 8080                  # Health check port (optional, uses main port if not set)
    path: "/health"             # Health check endpoint
    
  # Logging
  logging:
    level: "info"               # Log level: trace, debug, info, warn, error
    format: "json"              # Format: json, text
    output: "stdout"            # Output: stdout, stderr, file
    file: "/var/log/gateway.log" # Log file path (if output is file)
    
  # Tracing
  tracing:
    enabled: false
    endpoint: "${JAEGER_ENDPOINT}"
    service_name: "rust-litellm-gateway"
    
  # Alerting
  alerting:
    enabled: false
    
    # Slack integration
    slack:
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#alerts"
      
    # Email integration
    email:
      smtp_host: "${SMTP_HOST}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from: "<EMAIL>"
      to: ["<EMAIL>"]
```

## 🌍 Environment Variables

All configuration options can be overridden with environment variables using the pattern:
`GATEWAY_<SECTION>_<KEY>=value`

### Examples

```bash
# Server configuration
export GATEWAY_SERVER_HOST="0.0.0.0"
export GATEWAY_SERVER_PORT="8000"

# Provider configuration
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."

# Database configuration
export DATABASE_URL="postgresql://user:pass@localhost/gateway"
export REDIS_URL="redis://localhost:6379"

# Authentication
export JWT_SECRET="your-secret-key"
```

## 🔒 Security Best Practices

### 1. Secrets Management

```yaml
# Use environment variables for secrets
auth:
  jwt:
    secret: "${JWT_SECRET}"     # Never hardcode secrets

providers:
  - name: "openai"
    config:
      api_key: "${OPENAI_API_KEY}"  # Use env vars
```

### 2. Network Security

```yaml
server:
  host: "127.0.0.1"            # Bind to localhost in production
  tls:
    enabled: true              # Always use TLS in production
    cert_file: "/path/to/cert.pem"
    key_file: "/path/to/key.pem"
```

### 3. Access Control

```yaml
auth:
  rbac:
    enabled: true              # Enable RBAC
    default_role: "readonly"   # Minimal default permissions
```

## 📝 Configuration Examples

### Development Configuration

```yaml
# config/dev.yaml
server:
  host: "127.0.0.1"
  port: 8000
  workers: 2

providers:
  - name: "openai-dev"
    provider_type: "openai"
    config:
      api_key: "${OPENAI_API_KEY}"

storage:
  database:
    url: "postgresql://localhost/gateway_dev"
  redis:
    url: "redis://localhost:6379"

monitoring:
  logging:
    level: "debug"
```

### Production Configuration

```yaml
# config/production.yaml
server:
  host: "0.0.0.0"
  port: 8000
  workers: 8
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/gateway.pem"
    key_file: "/etc/ssl/private/gateway.key"

providers:
  - name: "openai-primary"
    provider_type: "openai"
    config:
      api_key: "${OPENAI_API_KEY}"
    weight: 100
    
  - name: "anthropic-backup"
    provider_type: "anthropic"
    config:
      api_key: "${ANTHROPIC_API_KEY}"
    weight: 50

storage:
  database:
    url: "${DATABASE_URL}"
    max_connections: 20
  redis:
    url: "${REDIS_URL}"
    max_connections: 20

monitoring:
  metrics:
    enabled: true
  tracing:
    enabled: true
    endpoint: "${JAEGER_ENDPOINT}"
  alerting:
    enabled: true
    slack:
      webhook_url: "${SLACK_WEBHOOK_URL}"
```

## 🔄 Configuration Validation

The gateway validates configuration on startup and provides detailed error messages:

```bash
# Example validation error
Error: Invalid configuration
  - server.port: must be between 1 and 65535
  - providers[0].config.api_key: required field missing
  - storage.database.url: invalid connection string format
```

## 🔧 Runtime Configuration Updates

Some configuration options support runtime updates without restart:

- Provider weights and priorities
- Rate limits
- Cache settings
- Log levels

```bash
# Update configuration via API (requires admin role)
curl -X PUT http://localhost:8000/admin/config \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{"providers": [{"name": "openai-primary", "weight": 150}]}'
```

---

For more configuration examples, see the [examples directory](../examples/) in the repository.
