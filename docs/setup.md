# 🚀 Rust LiteLLM Gateway 快速设置指南

## 问题说明

如果你看到这个错误：
```
Error: Database(Database(PgDatabaseError { severity: Fatal, code: "28000", message: "role \"gateway\" does not exist" ...
```

这是因为 PostgreSQL 数据库中没有创建相应的用户和数据库。

## 解决方案

### 方案 1: 使用 Docker (推荐)

这是最简单的方法，会自动设置 PostgreSQL 和 Redis：

```bash
# 1. 确保 Docker Desktop 已安装并运行
# 2. 运行设置脚本
./setup-dev.sh

# 3. 启动网关
cargo run --bin gateway
```

### 方案 2: 手动启动 Docker 服务

```bash
# 启动开发服务
docker-compose -f deployment/docker/docker-compose.dev.yml up -d postgres-dev redis-dev

# 等待服务启动
sleep 10

# 启动网关
cargo run --bin gateway
```

### 方案 3: 使用本地 PostgreSQL

如果你有本地安装的 PostgreSQL：

```sql
-- 连接到 PostgreSQL 并执行：
CREATE USER gateway_dev WITH PASSWORD 'dev_password';
CREATE DATABASE gateway_dev OWNER gateway_dev;
GRANT ALL PRIVILEGES ON DATABASE gateway_dev TO gateway_dev;
```

然后修改 `config/gateway.yaml` 中的数据库连接：
```yaml
storage:
  database:
    url: "postgresql://gateway_dev:dev_password@localhost:5432/gateway_dev"
```

### 方案 4: 使用不同的端口

如果端口 5433 被占用，可以修改 `deployment/docker/docker-compose.dev.yml`：

```yaml
ports:
  - "5434:5432"  # 改为其他端口
```

然后相应修改 `config/gateway.yaml` 中的端口。

## 服务信息

开发环境默认配置：

- **PostgreSQL**: `localhost:5433`
  - 用户名: `gateway_dev`
  - 密码: `dev_password`
  - 数据库: `gateway_dev`

- **Redis**: `localhost:6380`

## 常用命令

```bash
# 启动开发服务
docker-compose -f deployment/docker/docker-compose.dev.yml up -d postgres-dev redis-dev

# 查看服务状态
docker-compose -f deployment/docker/docker-compose.dev.yml ps

# 查看日志
docker-compose -f deployment/docker/docker-compose.dev.yml logs postgres-dev
docker-compose -f deployment/docker/docker-compose.dev.yml logs redis-dev

# 停止服务
docker-compose -f deployment/docker/docker-compose.dev.yml down

# 重置数据库（删除所有数据）
docker-compose -f deployment/docker/docker-compose.dev.yml down -v
```

## 故障排除

### Docker 相关问题

1. **Docker 未运行**
   ```
   Cannot connect to the Docker daemon at unix:///var/run/docker.sock
   ```
   解决：启动 Docker Desktop

2. **端口被占用**
   ```
   Error starting userland proxy: listen tcp 0.0.0.0:5433: bind: address already in use
   ```
   解决：修改 docker-compose.dev.yml 中的端口映射

3. **权限问题**
   ```
   permission denied while trying to connect to the Docker daemon socket
   ```
   解决：确保用户在 docker 组中，或使用 sudo

### 数据库连接问题

1. **连接超时**
   - 检查 Docker 容器是否正在运行
   - 检查端口是否正确
   - 等待更长时间让服务完全启动

2. **认证失败**
   - 检查用户名和密码是否正确
   - 确保数据库已创建

## 下一步

设置完成后，你可以：

1. 启动网关：`cargo run --bin gateway`
2. 测试健康检查：`curl http://localhost:8000/health`
3. 查看可用模型：`curl http://localhost:8000/v1/models`

更多信息请参考主 README.md 文件。
