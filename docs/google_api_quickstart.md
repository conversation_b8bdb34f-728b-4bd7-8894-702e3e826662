# Google API 快速开始指南

本指南将帮助您快速配置和使用Google AI服务（Gemini、Vertex AI）通过LiteLLM Gateway。

## 🚀 快速开始

### 1. 获取API密钥

#### Google AI Studio (推荐新手)
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 点击 "Create API Key"
3. 复制生成的API密钥

#### Vertex AI (企业用户)
1. 在 [Google Cloud Console](https://console.cloud.google.com/) 创建项目
2. 启用 Vertex AI API
3. 创建服务账户并下载JSON密钥文件

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp examples/.env.google .env

# 编辑配置文件
nano .env
```

最小配置：
```bash
GOOGLE_API_KEY=your_google_ai_studio_api_key
GATEWAY_PORT=8080
```

### 3. 启动服务

```bash
# 编译并启动
cargo run --bin gateway

# 或使用配置文件
cargo run --bin gateway -- --config examples/google_api_config.yaml
```

### 4. 测试API

```bash
# 使用测试脚本
./scripts/test_api.sh

# 或手动测试
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "Hello! 你好！"
      }
    ]
  }'
```

## 📋 支持的模型

### Gemini 系列
- **gemini-1.5-pro**: 最强性能，支持200万token上下文
- **gemini-1.5-flash**: 快速响应，成本较低
- **gemini-pro**: 标准版本
- **gemini-pro-vision**: 支持图像理解

### Vertex AI 模型
- **text-bison**: 文本生成
- **chat-bison**: 对话模式
- **text-embedding-004**: 文本嵌入

## 🔧 常用配置

### 基础聊天
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "解释一下人工智能"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 500
}
```

### 图像理解
```json
{
  "model": "gemini-pro-vision",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "描述这张图片"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://example.com/image.jpg"
          }
        }
      ]
    }
  ]
}
```

### 流式响应
```json
{
  "model": "gemini-1.5-flash",
  "messages": [
    {
      "role": "user",
      "content": "写一个短故事"
    }
  ],
  "stream": true,
  "max_tokens": 800
}
```

## 🎯 模型选择建议

| 用途 | 推荐模型 | 特点 |
|------|----------|------|
| 日常对话 | gemini-1.5-flash | 快速、经济 |
| 复杂分析 | gemini-1.5-pro | 高质量、长上下文 |
| 图像理解 | gemini-pro-vision | 多模态 |
| 代码生成 | gemini-1.5-pro | 准确性高 |
| 文本嵌入 | text-embedding-004 | 向量化 |

## 💡 最佳实践

### 1. 温度设置
- **创意写作**: 0.8-1.0
- **代码生成**: 0.1-0.3
- **问答对话**: 0.5-0.7
- **数据分析**: 0.1-0.4

### 2. Token管理
- 使用 `max_tokens` 控制响应长度
- Gemini 1.5 Pro 支持最多200万token上下文
- 监控token使用量以控制成本

### 3. 错误处理
```python
import requests

def call_gemini_api(prompt):
    try:
        response = requests.post(
            "http://localhost:8080/v1/chat/completions",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            },
            json={
                "model": "gemini-1.5-pro",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 500
            },
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API调用失败: {e}")
        return None
```

## 🔍 故障排除

### 常见问题

#### 1. API密钥无效
```
Error: 401 Unauthorized
```
**解决方案**: 检查API密钥是否正确，是否已启用相应服务

#### 2. 模型不可用
```
Error: Model not found
```
**解决方案**: 确认模型名称正确，检查配置文件中的模型列表

#### 3. 请求超时
```
Error: Request timeout
```
**解决方案**: 增加timeout设置，检查网络连接

#### 4. 速率限制
```
Error: 429 Too Many Requests
```
**解决方案**: 降低请求频率，升级API配额

### 调试技巧

1. **启用详细日志**:
```bash
RUST_LOG=debug cargo run --bin gateway
```

2. **检查健康状态**:
```bash
curl http://localhost:8080/health/detailed
```

3. **监控指标**:
```bash
curl http://localhost:8080/metrics
```

## 📚 更多资源

- [Google AI Studio 文档](https://ai.google.dev/)
- [Vertex AI 文档](https://cloud.google.com/vertex-ai/docs)
- [Gemini API 参考](https://ai.google.dev/api)
- [LiteLLM 文档](https://docs.litellm.ai/)

## 🤝 社区支持

- GitHub Issues: 报告问题和功能请求
- 讨论区: 技术交流和最佳实践分享
- 示例代码: 查看更多使用示例

## 📝 下一步

1. 尝试不同的模型和参数
2. 集成到您的应用程序中
3. 设置监控和告警
4. 优化性能和成本
5. 探索高级功能（函数调用、安全设置等）

开始您的Google AI之旅吧！🎉
