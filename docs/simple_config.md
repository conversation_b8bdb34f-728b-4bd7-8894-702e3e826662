# 🚀 Simple Configuration Guide

> **📚 Need more documentation?** Check out [Complete Documentation Index](documentation_index.md) | [Main README](../README.md) | [Detailed Configuration Guide](configuration.md)

## Get Started in Just 2 Steps!

### Step 1: Add Your API Keys

Edit the `config/gateway.yaml` file and add your API keys:

```yaml
providers:
  # OpenAI (ChatGPT)
  - name: "openai"
    provider_type: "openai"
    api_key: "sk-your-openai-api-key-here"  # 👈 Add your OpenAI API key

  # Anthropic (Claude)
  - name: "anthropic"
    provider_type: "anthropic"
    api_key: "sk-ant-your-anthropic-key-here"  # 👈 Add your Anthropic API key

  # Google (Gemini)
  - name: "google"
    provider_type: "google"
    api_key: "your-google-api-key-here"  # 👈 Add your Google API key
```

### Step 2: Start the Gateway

```bash
# Method 1: Direct startup
cargo run

# Method 2: Use startup script
./deployment/scripts/quick-start.sh
```

That's it! 🎉

**No command line arguments needed, no complex configuration, automatically loads `config/gateway.yaml`!**

## Success Indicators

When you see these messages, the startup was successful:

```
🚀 Starting Rust LiteLLM Gateway
📄 Loading configuration file: config/gateway.yaml
✅ Configuration file loaded successfully
🌐 Server started at: http://0.0.0.0:8000
📋 API endpoints:
   GET  /health - Health check
   GET  /v1/models - Model list
   POST /v1/chat/completions - Chat completions
```

## Test the API

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## Common Questions

**Q: I only have an OpenAI API key, what about the others?**
A: Just fill in OpenAI's key and delete or comment out the others.

**Q: What if the configuration file is not found?**
A: Make sure the `config/gateway.yaml` file exists. If it doesn't exist, the program will use default configuration but may not work properly.

**Q: What if the port is already in use?**
A: Change `port: 8000` to another port in `config/gateway.yaml`.

**Q: What if I want more features?**
A: Check the `config/gateway.yaml.example` file for complete configuration options.

## 🎯 Next Steps

After successful startup, you might want to:

### 📚 Learn More
- **[⚙️ Complete Configuration Guide](configuration.md)** - Learn about all configuration options
- **[🔌 API Reference](api.md)** - Learn how to use the API
- **[🧪 Usage Examples](../examples/basic_usage.md)** - View more usage examples

### 🚀 Production Deployment
- **[🚀 Deployment Guide](../deployment/README.md)** - Production environment deployment
- **[🐳 Docker Deployment](../deployment/docker/README.md)** - Deploy using Docker
- **[📜 Automation Scripts](../deployment/scripts/README.md)** - Use deployment scripts

### 🧪 Testing and Development
- **[🧪 API Testing](../tests/api_test_examples.md)** - Test API functionality
- **[🤝 Contributing Guide](contributing.md)** - Participate in project development

### 📚 Complete Documentation
- **[📚 Complete Documentation Index](documentation_index.md)** - View all available documentation
