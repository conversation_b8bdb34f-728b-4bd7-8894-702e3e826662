# Quick Start Guide

Get up and running with Rust LiteLLM Gateway in minutes!

## Prerequisites

- **Rust 1.85+** (for building from source)
- **PostgreSQL 12+** (for data storage)
- **Redis 6+** (for caching)
- **AI Provider API Keys** (OpenAI, Anthropic, etc.)

## Installation Options

### Option 1: Docker (Recommended)

The fastest way to get started:

```bash
# Clone the repository
git clone https://github.com/majiayu000/litellm-rs.git
cd rust-litellm-gateway

# Copy environment variables
cp .env.example .env
# Edit .env with your API keys

# Start with Docker Compose
docker-compose up -d
```

### Option 2: Pre-built Binaries

Download from [GitHub Releases](https://github.com/majiayu000/litellm-rs/releases):

```bash
# Download and extract (replace with actual version)
wget https://github.com/majiayu000/litellm-rs/releases/download/v0.1.0/rust-litellm-gateway-v0.1.0-linux-x86_64.tar.gz
tar -xzf rust-litellm-gateway-v0.1.0-linux-x86_64.tar.gz

# Make executable
chmod +x gateway

# Run
./gateway --config config/gateway.yaml
```

### Option 3: Build from Source

```bash
# Clone the repository
git clone https://github.com/majiayu000/litellm-rs.git
cd rust-litellm-gateway

# Build
cargo build --release

# Run
./target/release/gateway --config config/gateway.yaml
```

## Configuration

### 1. Environment Variables

Create a `.env` file with your API keys:

```bash
# Copy the example
cp .env.example .env

# Edit with your values
nano .env
```

Required variables:
```bash
# Database
DATABASE_URL=postgresql://gateway:password@localhost:5432/gateway
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# AI Providers (at least one required)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
```

### 2. Configuration File

Create `config/gateway.yaml`:

```bash
# Copy the example
cp config/gateway.yaml.example config/gateway.yaml

# Edit as needed
nano config/gateway.yaml
```

Minimal configuration:
```yaml
server:
  host: "127.0.0.1"
  port: 8000

providers:
  - name: "openai"
    provider_type: "openai"
    enabled: true
    config:
      api_key: "${OPENAI_API_KEY}"
    models:
      - "gpt-3.5-turbo"

storage:
  database:
    url: "${DATABASE_URL}"
  redis:
    url: "${REDIS_URL}"

auth:
  jwt:
    secret: "${JWT_SECRET}"
```

## Database Setup

### Using Docker (Recommended)

```bash
# Start PostgreSQL and Redis
docker-compose -f docker-compose.dev.yml up -d postgres-dev redis-dev
```

### Manual Setup

```bash
# PostgreSQL
createdb gateway
psql gateway < migrations/001_initial.sql

# Redis (usually no setup needed)
redis-server
```

## First Run

### 1. Start the Gateway

```bash
# From source
cargo run -- --config config/gateway.yaml

# From binary
./gateway --config config/gateway.yaml

# With Docker
docker-compose up gateway
```

### 2. Verify It's Running

```bash
# Health check
curl http://localhost:8000/health

# Expected response:
# {"status":"healthy","timestamp":"2024-01-15T10:30:00Z","version":"0.1.0"}
```

### 3. List Available Models

```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/v1/models
```

## Your First API Call

### Chat Completion

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ]
  }'
```

### Expected Response

```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 18,
    "total_tokens": 30
  }
}
```

## API Key Management

### Create an API Key

```bash
# Using the admin API (requires admin JWT token)
curl -X POST http://localhost:8000/admin/api-keys \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-jwt-token" \
  -d '{
    "name": "My First Key",
    "permissions": ["chat:create", "completion:create"]
  }'
```

### Or Use Default Development Key

For development, you can use a simple API key configured in your settings.

## Common Issues

### 1. Database Connection Error

```
Error: Failed to connect to database
```

**Solution:**
- Ensure PostgreSQL is running
- Check `DATABASE_URL` in `.env`
- Verify database exists and is accessible

### 2. Redis Connection Error

```
Error: Failed to connect to Redis
```

**Solution:**
- Ensure Redis is running
- Check `REDIS_URL` in `.env`
- Try: `redis-cli ping`

### 3. Provider API Error

```
Error: Invalid API key for provider 'openai'
```

**Solution:**
- Verify API key is correct in `.env`
- Check API key has sufficient credits/permissions
- Test API key directly with provider

### 4. Permission Denied

```
Error: Insufficient permissions
```

**Solution:**
- Check API key permissions
- Verify RBAC configuration
- Use admin token for admin operations

## Next Steps

### Development

1. **Explore Examples**: Check out [basic usage examples](../examples/basic_usage.md)
2. **Read API Docs**: Full [API reference](api.md)
3. **Configure Providers**: Add more [AI providers](configuration.md#providers)

### Production

1. **Security**: Review [security best practices](security.md)
2. **Monitoring**: Set up [monitoring and alerts](monitoring.md)
3. **Deployment**: Follow [deployment guide](deployment.md)

### Advanced Features

1. **Caching**: Configure [semantic caching](caching.md)
2. **Load Balancing**: Set up [intelligent routing](routing.md)
3. **Analytics**: Enable [usage analytics](analytics.md)

## Getting Help

- **Documentation**: [Full documentation](README.md)
- **Examples**: [Usage examples](../examples/)
- **Issues**: [GitHub Issues](https://github.com/majiayu000/litellm-rs/issues)
- **Discussions**: [GitHub Discussions](https://github.com/majiayu000/litellm-rs/discussions)

## Useful Commands

```bash
# Check version
./gateway --version

# Validate configuration
./gateway --config config/gateway.yaml --validate

# Run with debug logging
RUST_LOG=debug ./gateway --config config/gateway.yaml

# Run database migrations
./gateway --config config/gateway.yaml --migrate

# Generate API key
./gateway --config config/gateway.yaml --generate-key

# Health check with details
curl http://localhost:8000/health?detailed=true

# View metrics
curl http://localhost:8000/metrics

# Test all providers
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/admin/providers/test
```

Congratulations! You now have a running Rust LiteLLM Gateway. 🎉
