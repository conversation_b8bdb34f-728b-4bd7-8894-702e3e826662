# 📚 Rust LiteLLM Gateway - Complete Documentation Index

This is the complete documentation index for the Rust LiteLLM Gateway project, helping you quickly find the documentation you need.

## 🎯 Must-Read for Beginners

### 🚀 Super Quick Start
1. **[🎯 Simple Configuration Guide (simple_config.md)](simple_config.md)** ⭐⭐⭐
   - **Get started in just 2 steps!**
   - No complex configuration needed, auto-loads configuration files
   - Perfect for: Users who want immediate experience

2. **[📦 Setup Guide (setup.md)](setup.md)** ⭐⭐
   - Detailed installation steps
   - Environment requirements and dependencies
   - Perfect for: Users who need complete installation guidance

## 📖 Core Documentation

### 📚 Main Documentation
- **[📄 Main README (../README.md)](../README.md)** - Project overview and quick start
- **[📚 Documentation Overview (README.md)](README.md)** - Detailed index of all documentation
- **[⚙️ Configuration Guide (configuration.md)](configuration.md)** - Complete configuration reference
- **[🏗️ Architecture Overview (architecture.md)](architecture.md)** - System design and components
- **[🔌 API Reference (api.md)](api.md)** - Complete API documentation
- **[⚡ Quick Start Guide (quickstart.md)](quickstart.md)** - Detailed quick start guide

### 🌐 Specialized Guides
- **[🌐 Google API Quick Start (google_api_quickstart.md)](google_api_quickstart.md)** - Google API specific configuration

## 🚀 Deployment & Operations

### 📁 deployment/ Directory
- **[🚀 Deployment Overview (../deployment/README.md)](../deployment/README.md)** - Deployment strategy overview
- **[🐳 Docker Deployment (../deployment/docker/README.md)](../deployment/docker/README.md)** - Containerized deployment
- **[📜 Deployment Scripts (../deployment/scripts/README.md)](../deployment/scripts/README.md)** - Automation scripts

### 🐳 Docker Related
```
deployment/docker/
├── README.md                    # Docker deployment guide
├── Dockerfile                   # Docker image build
├── docker-compose.yml          # Production Docker Compose
└── docker-compose.dev.yml      # Development Docker Compose
```

### ⚙️ Kubernetes Deployment
```
deployment/kubernetes/
├── namespace.yaml               # Namespace
├── configmap.yaml              # Configuration map
├── secret.yaml                 # Secret configuration
├── deployment.yaml             # Deployment configuration
├── service.yaml                # Service configuration
└── ingress.yaml                # Ingress configuration
```

### 📜 Automation Scripts
```
deployment/scripts/
├── README.md                   # Script usage instructions
├── quick-start.sh              # Quick start script
├── setup.sh                   # Environment setup script
├── start.sh                   # Start script
├── start_google_gateway.sh     # Google API specific startup
├── docker-start.sh             # Docker startup script
├── test_api.sh                 # API test script
├── init-db.sql                 # Database initialization
└── init-dev-db.sql             # Development database initialization
```

### 🔧 System Service
```
deployment/systemd/
└── litellm-rs.service # Systemd service configuration
```

## 🧪 Examples & Testing

### 📁 examples/ Directory
- **[🧪 Basic Usage Examples (../examples/basic_usage.md)](../examples/basic_usage.md)** - Practical usage examples
- **[⚙️ Google API Configuration Example (../examples/google_api_config.yaml)](../examples/google_api_config.yaml)** - Google API configuration template

### 📁 tests/ Directory
- **[📝 API Test Examples (../tests/api_test_examples.md)](../tests/api_test_examples.md)** - API test cases
- **[🧪 Google API Tests (../tests/google_api_tests.md)](../tests/google_api_tests.md)** - Google API specific tests
- **[🧪 Integration Tests (../tests/integration_tests.rs)](../tests/integration_tests.rs)** - Rust integration test code

### 📋 Postman Collections
```
tests/
├── LiteLLM_Gateway.postman_collection.json     # Main API test collection
└── Google_API_Tests.postman_collection.json    # Google API test collection
```

## ⚙️ Configuration Files

### 📁 config/ Directory
```
config/
├── gateway.yaml                # Main configuration file (auto-loaded)
└── gateway.yaml.example        # Configuration file example
```

## 🛠️ Development Related

### 📄 Project Management Documentation
- **[🤝 Contributing Guide (contributing.md)](contributing.md)** - How to contribute to the project
- **[📋 Changelog (changelog.md)](changelog.md)** - Version history and changes
- **[📄 License (../LICENSE)](../LICENSE)** - MIT license
- **[📄 Original LiteLLM License (../LICENSE-LITELLM)](../LICENSE-LITELLM)** - Original project license

### 🔧 Build and Development
- **[🔧 Makefile (../Makefile)](../Makefile)** - Development commands
- **[🔧 Build Script (../build.rs)](../build.rs)** - Rust build script
- **[🚀 Development Setup Script (../setup-dev.sh)](../setup-dev.sh)** - Development environment setup
- **[🚀 Start Script (../start.sh)](../start.sh)** - Quick start script

## 🎯 Categorized by User Type

### 👤 New Users
1. [🎯 Simple Configuration Guide (simple_config.md)](simple_config.md) ⭐⭐⭐
2. [📦 Setup Guide (setup.md)](setup.md)
3. [🧪 Basic Usage Examples (../examples/basic_usage.md)](../examples/basic_usage.md)

### 👨‍💻 Developers
1. [🏗️ Architecture Overview (architecture.md)](architecture.md)
2. [🔌 API Reference (api.md)](api.md)
3. [🤝 Contributing Guide (contributing.md)](contributing.md)
4. [🧪 Integration Tests (../tests/integration_tests.rs)](../tests/integration_tests.rs)

### 🚀 DevOps/SRE
1. [🚀 Deployment Guide (../deployment/README.md)](../deployment/README.md)
2. [🐳 Docker Deployment (../deployment/docker/README.md)](../deployment/docker/README.md)
3. [📜 Deployment Scripts (../deployment/scripts/README.md)](../deployment/scripts/README.md)
4. [⚙️ Kubernetes Deployment](../deployment/kubernetes/)

### 👥 End Users
1. [🎯 Simple Configuration Guide (simple_config.md)](simple_config.md)
2. [⚙️ Configuration Guide (configuration.md)](configuration.md)
3. [🧪 Usage Examples (../examples/basic_usage.md)](../examples/basic_usage.md)
4. [📝 API Test Examples (../tests/api_test_examples.md)](../tests/api_test_examples.md)

## 🔍 Categorized by Feature

### 🌐 Google API Related
- [🌐 Google API Quick Start (google_api_quickstart.md)](google_api_quickstart.md)
- [⚙️ Google API Configuration Example (../examples/google_api_config.yaml)](../examples/google_api_config.yaml)
- [🧪 Google API Tests (../tests/google_api_tests.md)](../tests/google_api_tests.md)
- [🚀 Google API Start Script (../deployment/scripts/start_google_gateway.sh)](../deployment/scripts/start_google_gateway.sh)
- [📋 Google API Postman Collection (../tests/Google_API_Tests.postman_collection.json)](../tests/Google_API_Tests.postman_collection.json)

### 🐳 Containerized Deployment
- [🐳 Docker Deployment Guide (../deployment/docker/README.md)](../deployment/docker/README.md)
- [⚙️ Kubernetes Manifest Files](../deployment/kubernetes/)
- [🚀 Docker Start Script (../deployment/scripts/docker-start.sh)](../deployment/scripts/docker-start.sh)

### 🧪 Testing Related
- [📝 API Test Examples (../tests/api_test_examples.md)](../tests/api_test_examples.md)
- [🧪 Google API Tests (../tests/google_api_tests.md)](../tests/google_api_tests.md)
- [🧪 Integration Test Code (../tests/integration_tests.rs)](../tests/integration_tests.rs)
- [🚀 API Test Script (../deployment/scripts/test_api.sh)](../deployment/scripts/test_api.sh)
- [📋 Postman Test Collections](../tests/)

## 📞 Getting Help

If you encounter issues while using:

1. **Check relevant documentation** - Find the corresponding documentation based on the categories above
2. **Check examples** - View examples in the [examples/](../examples/) directory
3. **Run tests** - Use test cases in the [tests/](../tests/) directory
4. **Submit issues** - Submit issues on GitHub Issues

## 🔄 Documentation Updates

This documentation is updated with the project. If you find outdated or incorrect documentation, please:
1. Submit an Issue to report the problem
2. Submit a Pull Request to fix the documentation
3. Refer to the [Contributing Guide (contributing.md)](contributing.md)

---

**💡 Tip**: New users are recommended to start with [🎯 Simple Configuration Guide (simple_config.md)](simple_config.md)!

**🏠 Return to Homepage**: [README.md](../README.md)
