# API Reference

This document provides a complete reference for the Rust LiteLLM Gateway API, including all endpoints, request/response formats, and examples.

## 🔗 Base URL

```
https://your-gateway-domain.com
```

## 🔐 Authentication

The gateway supports multiple authentication methods:

### 1. API Key Authentication

```bash
curl -H "Authorization: Bearer your-api-key" \
  https://your-gateway-domain.com/v1/models
```

### 2. JWT Token Authentication

```bash
curl -H "Authorization: Bearer your-jwt-token" \
  https://your-gateway-domain.com/v1/models
```

## 📋 OpenAI Compatible Endpoints

### Chat Completions

Create a chat completion response.

**Endpoint:** `POST /v1/chat/completions`

**Request Body:**
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user", 
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 150,
  "top_p": 1.0,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0,
  "stream": false
}
```

**Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 20,
    "completion_tokens": 18,
    "total_tokens": 38
  }
}
```

### Text Completions

Create a completion response.

**Endpoint:** `POST /v1/completions`

**Request Body:**
```json
{
  "model": "gpt-3.5-turbo-instruct",
  "prompt": "Once upon a time",
  "max_tokens": 100,
  "temperature": 0.7,
  "top_p": 1.0,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0,
  "stream": false
}
```

**Response:**
```json
{
  "id": "cmpl-123",
  "object": "text_completion",
  "created": **********,
  "model": "gpt-3.5-turbo-instruct",
  "choices": [
    {
      "text": ", in a land far away, there lived a brave knight...",
      "index": 0,
      "finish_reason": "length"
    }
  ],
  "usage": {
    "prompt_tokens": 4,
    "completion_tokens": 100,
    "total_tokens": 104
  }
}
```

### Embeddings

Create embeddings for text inputs.

**Endpoint:** `POST /v1/embeddings`

**Request Body:**
```json
{
  "model": "text-embedding-ada-002",
  "input": [
    "The quick brown fox jumps over the lazy dog",
    "Hello world"
  ]
}
```

**Response:**
```json
{
  "object": "list",
  "data": [
    {
      "object": "embedding",
      "index": 0,
      "embedding": [0.0023064255, -0.009327292, ...]
    },
    {
      "object": "embedding", 
      "index": 1,
      "embedding": [-0.0019608222, 0.015757175, ...]
    }
  ],
  "model": "text-embedding-ada-002",
  "usage": {
    "prompt_tokens": 13,
    "total_tokens": 13
  }
}
```

### Models

List available models.

**Endpoint:** `GET /v1/models`

**Response:**
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-4",
      "object": "model",
      "created": 1677610602,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-4",
      "parent": null
    },
    {
      "id": "gpt-3.5-turbo",
      "object": "model", 
      "created": 1677610602,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-3.5-turbo",
      "parent": null
    }
  ]
}
```

## 🔄 Streaming Responses

### Chat Completions Streaming

**Request:**
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [{"role": "user", "content": "Tell me a story"}],
  "stream": true
}
```

**Response (Server-Sent Events):**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Once"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":" upon"},"finish_reason":null}]}

data: [DONE]
```

## 🛠️ Gateway-Specific Endpoints

### Health Check

Check gateway health status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "0.1.0",
  "components": {
    "database": {
      "status": "healthy",
      "response_time_ms": 5
    },
    "redis": {
      "status": "healthy", 
      "response_time_ms": 2
    },
    "providers": {
      "openai": {
        "status": "healthy",
        "response_time_ms": 150
      },
      "anthropic": {
        "status": "healthy",
        "response_time_ms": 200
      }
    }
  }
}
```

### Metrics

Get Prometheus metrics.

**Endpoint:** `GET /metrics`

**Response:**
```
# HELP gateway_requests_total Total number of requests
# TYPE gateway_requests_total counter
gateway_requests_total{method="POST",endpoint="/v1/chat/completions",status="200"} 1234

# HELP gateway_request_duration_seconds Request duration in seconds
# TYPE gateway_request_duration_seconds histogram
gateway_request_duration_seconds_bucket{le="0.1"} 100
gateway_request_duration_seconds_bucket{le="0.5"} 450
gateway_request_duration_seconds_bucket{le="1.0"} 800
```

## 🔧 Admin Endpoints

### User Management

#### Create User

**Endpoint:** `POST /admin/users`

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "secure_password",
  "role": "user",
  "team_id": 1
}
```

**Response:**
```json
{
  "id": 123,
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "user",
  "team_id": 1,
  "created_at": "2024-01-15T10:30:00Z",
  "is_active": true
}
```

#### List Users

**Endpoint:** `GET /admin/users`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `role`: Filter by role
- `team_id`: Filter by team

**Response:**
```json
{
  "users": [
    {
      "id": 123,
      "username": "john_doe",
      "email": "<EMAIL>",
      "role": "user",
      "team_id": 1,
      "created_at": "2024-01-15T10:30:00Z",
      "is_active": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

### API Key Management

#### Create API Key

**Endpoint:** `POST /admin/api-keys`

**Request Body:**
```json
{
  "name": "Production Key",
  "user_id": 123,
  "permissions": ["chat:create", "completion:create"],
  "rate_limit": {
    "requests_per_minute": 100,
    "tokens_per_minute": 10000
  },
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**Response:**
```json
{
  "id": "key_456",
  "name": "Production Key",
  "key": "sk-abc123...",
  "user_id": 123,
  "permissions": ["chat:create", "completion:create"],
  "rate_limit": {
    "requests_per_minute": 100,
    "tokens_per_minute": 10000
  },
  "created_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-12-31T23:59:59Z",
  "is_active": true
}
```

#### List API Keys

**Endpoint:** `GET /admin/api-keys`

**Response:**
```json
{
  "api_keys": [
    {
      "id": "key_456",
      "name": "Production Key",
      "user_id": 123,
      "permissions": ["chat:create", "completion:create"],
      "created_at": "2024-01-15T10:30:00Z",
      "expires_at": "2024-12-31T23:59:59Z",
      "is_active": true,
      "last_used": "2024-01-15T12:00:00Z"
    }
  ]
}
```

### Provider Management

#### List Providers

**Endpoint:** `GET /admin/providers`

**Response:**
```json
{
  "providers": [
    {
      "name": "openai-primary",
      "provider_type": "openai",
      "enabled": true,
      "weight": 100,
      "health_status": "healthy",
      "models": ["gpt-4", "gpt-3.5-turbo"],
      "last_health_check": "2024-01-15T10:29:00Z",
      "stats": {
        "requests_total": 1234,
        "requests_success": 1200,
        "requests_failed": 34,
        "avg_response_time_ms": 250
      }
    }
  ]
}
```

#### Update Provider

**Endpoint:** `PUT /admin/providers/{name}`

**Request Body:**
```json
{
  "enabled": true,
  "weight": 150,
  "rate_limit": {
    "requests_per_minute": 1000
  }
}
```

## 📊 Analytics Endpoints

### Usage Statistics

**Endpoint:** `GET /admin/analytics/usage`

**Query Parameters:**
- `start_date`: Start date (ISO 8601)
- `end_date`: End date (ISO 8601)
- `granularity`: hour, day, week, month
- `user_id`: Filter by user
- `model`: Filter by model

**Response:**
```json
{
  "period": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-31T23:59:59Z",
    "granularity": "day"
  },
  "data": [
    {
      "timestamp": "2024-01-01T00:00:00Z",
      "requests": 1234,
      "tokens": 567890,
      "cost_usd": 12.34,
      "avg_response_time_ms": 250
    }
  ],
  "summary": {
    "total_requests": 38456,
    "total_tokens": 17654321,
    "total_cost_usd": 384.56,
    "avg_response_time_ms": 245
  }
}
```

### Cost Analysis

**Endpoint:** `GET /admin/analytics/costs`

**Response:**
```json
{
  "by_provider": [
    {
      "provider": "openai",
      "cost_usd": 234.56,
      "requests": 12345,
      "tokens": 6789012
    }
  ],
  "by_model": [
    {
      "model": "gpt-4",
      "cost_usd": 123.45,
      "requests": 1234,
      "tokens": 567890
    }
  ],
  "by_user": [
    {
      "user_id": 123,
      "username": "john_doe",
      "cost_usd": 45.67,
      "requests": 890,
      "tokens": 123456
    }
  ]
}
```

## ❌ Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "type": "invalid_request_error",
    "code": "invalid_api_key",
    "message": "Invalid API key provided",
    "details": {
      "field": "authorization",
      "reason": "The API key is malformed or expired"
    }
  }
}
```

### Error Types

- `authentication_error`: Authentication failed
- `authorization_error`: Insufficient permissions
- `invalid_request_error`: Invalid request format
- `rate_limit_error`: Rate limit exceeded
- `provider_error`: Provider-specific error
- `internal_error`: Internal server error

### HTTP Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error
- `502`: Bad Gateway (provider error)
- `503`: Service Unavailable

## 📝 Request/Response Headers

### Common Request Headers

```
Authorization: Bearer your-api-key
Content-Type: application/json
User-Agent: YourApp/1.0
X-Request-ID: unique-request-id
```

### Common Response Headers

```
Content-Type: application/json
X-Request-ID: unique-request-id
X-RateLimit-Remaining: 99
X-RateLimit-Reset: **********
X-Response-Time-Ms: 250
```

## 🔄 Webhooks

### Webhook Events

The gateway can send webhooks for various events:

- `request.completed`: Request completed successfully
- `request.failed`: Request failed
- `user.created`: New user created
- `api_key.created`: New API key created
- `provider.health_changed`: Provider health status changed

### Webhook Payload

```json
{
  "event": "request.completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "request_id": "req_123",
    "user_id": 123,
    "model": "gpt-3.5-turbo",
    "provider": "openai",
    "tokens_used": 150,
    "cost_usd": 0.003,
    "response_time_ms": 250
  }
}
```

## 📚 SDKs and Libraries

### Official SDKs

- **Python**: `pip install rust-litellm-gateway`
- **JavaScript/Node.js**: `npm install rust-litellm-gateway`
- **Go**: `go get github.com/majiayu000/litellm-rs-go`

### Community SDKs

- **Java**: Available on Maven Central
- **C#**: Available on NuGet
- **Ruby**: Available on RubyGems

## 🧪 Testing

### Test Endpoints

**Health Check:**
```bash
curl http://localhost:8000/health
```

**List Models:**
```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/v1/models
```

**Simple Chat:**
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 50
  }'
```

---

For more examples and detailed usage, see the [examples directory](../examples/) in the repository.
