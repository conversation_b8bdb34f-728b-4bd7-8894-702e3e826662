# Prometheus Configuration for Rust LiteLLM Gateway

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'rust-litellm-gateway'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Rust LiteLLM Gateway
  - job_name: 'rust-litellm-gateway'
    static_configs:
      - targets: ['gateway:9090']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Gateway health endpoint
  - job_name: 'gateway-health'
    static_configs:
      - targets: ['gateway:8000']
    scrape_interval: 30s
    metrics_path: /health
    scrape_timeout: 5s

  # PostgreSQL metrics (if using postgres_exporter)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis metrics (if using redis_exporter)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker metrics (if using cadvisor)
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx metrics (if using nginx-prometheus-exporter)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration
# remote_read:
#   - url: "https://your-remote-storage/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
