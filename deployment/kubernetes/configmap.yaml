apiVersion: v1
kind: ConfigMap
metadata:
  name: litellm-gateway-config
  namespace: litellm-gateway
  labels:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: config
data:
  gateway.yaml: |
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      timeout: 30
      max_body_size: 10485760
      
    providers:
      - name: "openai-primary"
        provider_type: "openai"
        enabled: true
        weight: 100
        config:
          api_key: "${OPENAI_API_KEY}"
          base_url: "https://api.openai.com/v1"
        models:
          - "gpt-4"
          - "gpt-3.5-turbo"
        health_check:
          enabled: true
          interval: 30
          timeout: 10
          retries: 3
          
      - name: "anthropic-backup"
        provider_type: "anthropic"
        enabled: true
        weight: 80
        config:
          api_key: "${ANTHROPIC_API_KEY}"
          base_url: "https://api.anthropic.com"
        models:
          - "claude-3-opus-20240229"
          - "claude-3-sonnet-20240229"
          
    router:
      strategy: "least_latency"
      health_check_interval: 30
      retry_attempts: 3
      provider_timeout: 30
      
    auth:
      jwt:
        enabled: true
        secret: "${JWT_SECRET}"
        expiration: 3600
      api_key:
        enabled: true
        header: "Authorization"
      rbac:
        enabled: true
        default_role: "user"
        
    storage:
      database:
        url: "${DATABASE_URL}"
        max_connections: 10
        connection_timeout: 30
      redis:
        url: "${REDIS_URL}"
        max_connections: 10
        connection_timeout: 5
        
    caching:
      memory:
        enabled: true
        max_size: 1000
        ttl: 300
      redis:
        enabled: true
        ttl: 3600
        key_prefix: "gateway:"
        
    monitoring:
      metrics:
        enabled: true
        port: 9090
        path: "/metrics"
      health:
        enabled: true
        path: "/health"
      logging:
        level: "info"
        format: "json"
      tracing:
        enabled: true
        endpoint: "${JAEGER_ENDPOINT}"
        service_name: "rust-litellm-gateway"
