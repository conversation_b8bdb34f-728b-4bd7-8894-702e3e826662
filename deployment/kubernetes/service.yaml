apiVersion: v1
kind: Service
metadata:
  name: litellm-gateway-service
  namespace: litellm-gateway
  labels:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: gateway
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: gateway

---
apiVersion: v1
kind: Service
metadata:
  name: litellm-gateway-headless
  namespace: litellm-gateway
  labels:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: gateway
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  selector:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: gateway
