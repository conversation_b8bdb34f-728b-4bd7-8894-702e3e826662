apiVersion: v1
kind: Secret
metadata:
  name: litellm-gateway-secrets
  namespace: litellm-gateway
  labels:
    app.kubernetes.io/name: litellm-gateway
    app.kubernetes.io/component: secrets
type: Opaque
stringData:
  # Database connection
  DATABASE_URL: "***************************************************/gateway"
  REDIS_URL: "redis://redis-service:6379"
  
  # JWT secret
  JWT_SECRET: "your-super-secret-jwt-key-change-this-in-production"
  
  # Provider API keys
  OPENAI_API_KEY: "sk-your-openai-api-key"
  ANTHROPIC_API_KEY: "sk-ant-your-anthropic-api-key"
  GOOGLE_AI_API_KEY: "your-google-ai-api-key"
  AZURE_OPENAI_API_KEY: "your-azure-openai-api-key"
  AZURE_OPENAI_ENDPOINT: "https://your-resource.openai.azure.com"
  COHERE_API_KEY: "your-cohere-api-key"
  
  # S3 configuration (optional)
  AWS_ACCESS_KEY_ID: "your-aws-access-key"
  AWS_SECRET_ACCESS_KEY: "your-aws-secret-key"
  AWS_REGION: "us-east-1"
  S3_BUCKET: "your-s3-bucket"
  
  # Monitoring
  JAEGER_ENDPOINT: "http://jaeger-service:14268/api/traces"
  
  # SMTP configuration (optional)
  SMTP_HOST: "smtp.example.com"
  SMTP_PORT: "587"
  SMTP_USERNAME: "your-smtp-username"
  SMTP_PASSWORD: "your-smtp-password"
