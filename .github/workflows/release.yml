name: Release

on:
  push:
    tags:
      - 'v*'

env:
  CARGO_TERM_COLOR: always

jobs:
  # Create GitHub release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      version: ${{ steps.get_version.outputs.version }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Get version
      id: get_version
      run: echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ steps.get_version.outputs.version }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: false

  # Build and upload binaries
  build-release:
    name: Build Release
    needs: create-release
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            name: linux-x86_64
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            name: linux-x86_64-musl
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            name: windows-x86_64
          - os: macos-latest
            target: x86_64-apple-darwin
            name: macos-x86_64
          - os: macos-latest
            target: aarch64-apple-darwin
            name: macos-aarch64

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-${{ matrix.target }}-cargo-release-${{ hashFiles('**/Cargo.lock') }}

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev pkg-config libssl-dev
        if [ "${{ matrix.target }}" = "x86_64-unknown-linux-musl" ]; then
          sudo apt-get install -y musl-tools
        fi

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install postgresql openssl pkg-config

    - name: Build release binary
      run: cargo build --release --target ${{ matrix.target }} --all-features

    - name: Create archive (Unix)
      if: matrix.os != 'windows-latest'
      run: |
        cd target/${{ matrix.target }}/release
        tar czf ../../../rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.tar.gz gateway google-gateway
        cd ../../..

    - name: Create archive (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        cd target/${{ matrix.target }}/release
        7z a ../../../rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.zip gateway.exe google-gateway.exe
        cd ../../..

    - name: Upload Release Asset (Unix)
      if: matrix.os != 'windows-latest'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ./rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.tar.gz
        asset_name: rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.tar.gz
        asset_content_type: application/gzip

    - name: Upload Release Asset (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: ./rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.zip
        asset_name: rust-litellm-gateway-${{ needs.create-release.outputs.version }}-${{ matrix.name }}.zip
        asset_content_type: application/zip

  # Build and push Docker image
  docker-release:
    name: Docker Release
    needs: create-release
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          majiayu000/litellm-rs
          ghcr.io/${{ github.repository }}
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Publish to crates.io
  publish-crate:
    name: Publish to crates.io
    needs: create-release
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-publish-${{ hashFiles('**/Cargo.lock') }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev pkg-config libssl-dev

    - name: Publish to crates.io
      run: cargo publish --token ${{ secrets.CRATES_IO_TOKEN }}

  # Update Homebrew formula (for macOS users)
  homebrew-release:
    name: Update Homebrew Formula
    needs: [create-release, build-release]
    runs-on: ubuntu-latest
    if: github.repository == 'majiayu000/litellm-rs'
    steps:
    - name: Update Homebrew formula
      uses: mislav/bump-homebrew-formula-action@v3
      with:
        formula-name: rust-litellm-gateway
        homebrew-tap: majiayu000/homebrew-tap
        base-branch: main
        download-url: https://github.com/${{ github.repository }}/releases/download/${{ needs.create-release.outputs.version }}/rust-litellm-gateway-${{ needs.create-release.outputs.version }}-macos-x86_64.tar.gz
      env:
        COMMITTER_TOKEN: ${{ secrets.HOMEBREW_TOKEN }}

  # Deploy to staging environment
  deploy-staging:
    name: Deploy to Staging
    needs: [create-release, docker-release]
    runs-on: ubuntu-latest
    environment: staging
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying version ${{ needs.create-release.outputs.version }} to staging"
        # Add your staging deployment commands here
        # For example, update Kubernetes deployment, trigger ArgoCD sync, etc.

  # Deploy to production (manual approval required)
  deploy-production:
    name: Deploy to Production
    needs: [create-release, docker-release, deploy-staging]
    runs-on: ubuntu-latest
    environment: production
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying version ${{ needs.create-release.outputs.version }} to production"
        # Add your production deployment commands here
