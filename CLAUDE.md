# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development Commands
- **Start development**: `make dev` or `cargo run` (auto-loads config/gateway.yaml)
- **Build**: `cargo build --all-features` 
- **Test**: `cargo test --all-features`
- **Lint**: `cargo clippy --all-targets --all-features -- -D warnings`
- **Format**: `cargo fmt --all`
- **Quick start**: `make start` (fastest way to start the gateway)

### Testing Commands
- **All tests**: `make test`
- **Unit tests only**: `make test-unit` 
- **Integration tests**: `make test-integration`
- **Test coverage**: `make test-coverage`
- **Single test**: `cargo test <test_name> --all-features`

### Development Services
- **Start dev services**: `make dev-services` (starts PostgreSQL, Redis)
- **Stop dev services**: `make dev-stop`
- **Database migration**: `make db-migrate`
- **Reset database**: `make db-reset`

## Architecture Overview

This is a **high-performance AI Gateway** written in Rust that provides OpenAI-compatible APIs with intelligent routing across 20+ AI providers.

### Core Components

**Gateway Architecture**: Modular, trait-based design with dependency injection
- `src/core/` - Central orchestrator and business logic
- `src/server/` - Actix-web HTTP server with middleware pipeline  
- `src/auth/` - Multi-layered authentication (JWT, API keys, RBAC)
- `src/core/providers/` - Pluggable provider system (OpenAI, Anthropic, Azure, Google, etc.)
- `src/core/router/` - Intelligent routing with multiple strategies
- `src/storage/` - Multi-backend storage (PostgreSQL, Redis, S3, Vector DB)
- `src/monitoring/` - Observability (Prometheus, tracing, health checks)

### Key Design Patterns
- **Async-first**: All I/O is non-blocking using Tokio
- **Trait-based abstractions**: Pluggable components via traits
- **Error handling**: Comprehensive error types with context preservation
- **Configuration**: Type-safe config models with Default implementations

### Provider Integration
- **Unified Provider trait**: Common interface for all AI providers
- **Format conversion**: Automatic translation between OpenAI and provider-specific APIs
- **Health monitoring**: Per-provider health checks and failover
- **Cost calculation**: Built-in token counting and cost estimation

### Request Flow
1. HTTP Request → Authentication → Authorization → Router → Provider → Response
2. Middleware pipeline handles auth, logging, metrics, and transformations
3. Intelligent routing selects optimal provider based on health, latency, cost

## Configuration

- **Main config**: `config/gateway.yaml` (auto-loaded by default)
- **Example config**: `config/gateway.yaml.example`
- **Environment variables**: Override config values with `${ENV_VAR}` syntax
- **Config validation**: `make config-validate`

## Important Files

- `src/main.rs` - Application entry point
- `src/lib.rs` - Library entry point with core Gateway struct
- `Cargo.toml` - Dependencies and features (use `--all-features` for development)
- `Makefile` - All development commands and workflows
- `config/gateway.yaml` - Main configuration file

## Binaries

- `gateway` (default) - Main gateway server
- `google-gateway` - Specialized Google API gateway

## Features

The codebase uses Cargo features extensively:
- **Storage**: `postgres`, `sqlite`, `redis`, `s3`
- **Monitoring**: `metrics`, `tracing` 
- **Advanced**: `vector-db`, `websockets`, `analytics`, `enterprise`
- **Development**: Use `--all-features` flag for full functionality

## Database & Storage

- **Primary DB**: PostgreSQL with SQLx migrations
- **Cache**: Redis for high-speed operations
- **File storage**: S3-compatible object storage
- **Vector DB**: Optional Qdrant integration for semantic caching

## Testing Architecture

- Unit tests in each module (`#[cfg(test)]`)
- Integration tests in `tests/integration_tests.rs`
- Postman collections for API testing
- Mock implementations for external services

## Common Development Patterns

1. **Adding new providers**: Implement the `Provider` trait in `src/core/providers/`
2. **New API endpoints**: Add routes in `src/server/routes/`
3. **Authentication**: Extend auth modules in `src/auth/`
4. **Configuration**: Update models in `src/config/models/`
5. **Monitoring**: Add metrics in respective modules

## Docker & Deployment

- **Docker build**: `make docker`
- **Development stack**: `make docker-compose-dev`
- **Production**: `make docker-compose`
- **Kubernetes**: `make k8s-apply`

## Performance Characteristics

- **Throughput**: 10,000+ requests/second
- **Latency**: <10ms routing overhead
- **Memory**: ~50MB base footprint
- **Architecture**: Fully async, connection pooling, zero-copy where possible