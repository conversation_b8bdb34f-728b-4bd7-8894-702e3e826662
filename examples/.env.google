# Google API 环境变量配置示例
# 复制此文件为 .env 并填入您的实际API密钥

# =============================================================================
# Google AI Studio (Gemini API) 配置
# =============================================================================
# 获取方式：访问 https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_ai_studio_api_key_here

# =============================================================================
# Vertex AI 配置
# =============================================================================
# GCP项目ID
GOOGLE_PROJECT_ID=your_gcp_project_id

# GCP区域
GOOGLE_REGION=us-central1

# 服务账户密钥文件路径
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# 或者直接使用访问令牌
GOOGLE_VERTEX_ACCESS_TOKEN=your_vertex_ai_access_token

# =============================================================================
# PaLM API 配置 (已弃用，建议使用Gemini)
# =============================================================================
GOOGLE_PALM_API_KEY=your_palm_api_key

# =============================================================================
# LiteLLM Gateway 配置
# =============================================================================
# 服务器配置
GATEWAY_HOST=0.0.0.0
GATEWAY_PORT=8080
GATEWAY_WORKERS=4

# 日志级别
RUST_LOG=info

# 数据库配置 (可选)
DATABASE_URL=postgresql://user:password@localhost:5432/litellm_gateway

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 (如果启用认证)
JWT_SECRET=your_jwt_secret_key

# API密钥 (用于客户端认证)
API_KEY=your_gateway_api_key

# =============================================================================
# 监控配置
# =============================================================================
# Prometheus指标端口
METRICS_PORT=9090

# 健康检查配置
HEALTH_CHECK_ENABLED=true

# =============================================================================
# 提供商特定配置
# =============================================================================
# Google API配置
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com
GOOGLE_API_VERSION=v1beta

# Vertex AI配置
VERTEX_BASE_URL=https://us-central1-aiplatform.googleapis.com
VERTEX_API_VERSION=v1

# 请求限制
GOOGLE_RPM=60
GOOGLE_TPM=32000
VERTEX_RPM=300
VERTEX_TPM=100000

# 超时设置
GOOGLE_TIMEOUT=30
VERTEX_TIMEOUT=60

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1000

# =============================================================================
# 模型配置
# =============================================================================
# 默认模型映射
DEFAULT_CHAT_MODEL=gemini-1.5-pro
DEFAULT_COMPLETION_MODEL=gemini-1.5-flash
DEFAULT_EMBEDDING_MODEL=text-embedding-004

# 模型别名
GPT_3_5_TURBO_ALIAS=gemini-1.5-flash
GPT_4_ALIAS=gemini-1.5-pro
GPT_4_TURBO_ALIAS=gemini-1.5-pro

# =============================================================================
# 缓存配置
# =============================================================================
# 启用缓存
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# 速率限制配置
# =============================================================================
# 全局速率限制
GLOBAL_RPM=1000
GLOBAL_TPM=50000

# 用户级别限制
USER_RPM=100
USER_TPM=10000

# =============================================================================
# 安全和过滤配置
# =============================================================================
# 内容过滤
CONTENT_FILTER_ENABLED=true
SAFETY_THRESHOLD=BLOCK_MEDIUM_AND_ABOVE

# =============================================================================
# 开发和调试配置
# =============================================================================
# 开发模式
DEBUG=false
DEVELOPMENT_MODE=false

# 详细日志
VERBOSE_LOGGING=false

# 请求日志
LOG_REQUESTS=true
LOG_RESPONSES=false

# =============================================================================
# 使用说明
# =============================================================================
# 1. 复制此文件为 .env
# 2. 填入您的实际API密钥和配置
# 3. 确保 .env 文件不被提交到版本控制系统
# 4. 运行 cargo run --bin gateway 启动服务
# 5. 使用提供的测试脚本验证配置

# =============================================================================
# 获取API密钥的步骤
# =============================================================================
# Google AI Studio:
# 1. 访问 https://makersuite.google.com/app/apikey
# 2. 点击 "Create API Key"
# 3. 复制生成的API密钥

# Vertex AI:
# 1. 在 Google Cloud Console 创建项目
# 2. 启用 Vertex AI API
# 3. 创建服务账户
# 4. 下载服务账户密钥JSON文件
# 5. 设置 GOOGLE_APPLICATION_CREDENTIALS 环境变量

# =============================================================================
# 测试配置
# =============================================================================
# 运行测试脚本验证配置：
# ./scripts/test_api.sh
# python scripts/test_api.py --base-url http://localhost:8080 --api-key your-api-key
