# Google API 配置示例
# 支持 Google AI Studio (Gemini) 和 Vertex AI

gateway:
  server:
    host: "0.0.0.0"
    port: 8080
    workers: 4

  # Google API 提供商配置
  providers:
    # Google AI Studio (Gemini API) - 免费层
    - name: "google-gemini"
      provider_type: "google"
      api_key: "${GOOGLE_API_KEY}"  # 从环境变量获取
      base_url: "https://generativelanguage.googleapis.com"
      api_version: "v1beta"
      models:
        - "gemini-pro"
        - "gemini-pro-vision"
        - "gemini-1.5-pro"
        - "gemini-1.5-flash"
      weight: 1.0
      rpm: 60  # 免费层限制
      tpm: 32000
      max_concurrent_requests: 5
      timeout: 30
      enabled: true
      tags: ["google", "gemini", "free"]

    # Vertex AI - 企业级
    - name: "google-vertex"
      provider_type: "vertex"
      api_key: "${GOOGLE_VERTEX_API_KEY}"
      base_url: "https://us-central1-aiplatform.googleapis.com"
      project: "${GOOGLE_PROJECT_ID}"  # GCP项目ID
      region: "us-central1"
      models:
        - "gemini-1.5-pro"
        - "gemini-1.5-flash"
        - "text-bison"
        - "chat-bison"
        - "text-embedding-004"
      weight: 2.0
      rpm: 300  # 更高的限制
      tpm: 100000
      max_concurrent_requests: 10
      timeout: 60
      enabled: true
      tags: ["google", "vertex", "enterprise"]

    # PaLM API (传统)
    - name: "google-palm"
      provider_type: "palm"
      api_key: "${GOOGLE_PALM_API_KEY}"
      base_url: "https://generativelanguage.googleapis.com"
      api_version: "v1beta"
      models:
        - "text-bison-001"
        - "chat-bison-001"
        - "embedding-gecko-001"
      weight: 0.5
      rpm: 60
      tpm: 20000
      enabled: false  # 已弃用，建议使用Gemini
      tags: ["google", "palm", "deprecated"]

  # 路由配置
  router:
    strategy: "weighted_round_robin"
    fallback_enabled: true
    
    # 模型映射 - 将标准模型名映射到Google模型
    model_mapping:
      "gpt-3.5-turbo": "gemini-1.5-flash"
      "gpt-4": "gemini-1.5-pro"
      "gpt-4-turbo": "gemini-1.5-pro"
      "text-embedding-ada-002": "text-embedding-004"

    circuit_breaker:
      enabled: true
      failure_threshold: 5
      recovery_timeout: 60
      min_requests: 10

  # 认证配置
  auth:
    enable_jwt: false
    api_key_header: "Authorization"
    
  # 监控配置
  monitoring:
    metrics:
      enabled: true
      port: 9090
      path: "/metrics"
    
    health:
      path: "/health"
      detailed: true

# 环境变量示例 (.env 文件)
# GOOGLE_API_KEY=your_google_ai_studio_api_key
# GOOGLE_VERTEX_API_KEY=your_vertex_ai_service_account_key
# GOOGLE_PROJECT_ID=your_gcp_project_id
# GOOGLE_PALM_API_KEY=your_palm_api_key
