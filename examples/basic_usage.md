# Basic Usage Examples

This document provides practical examples of how to use the Rust LiteLLM Gateway.

## Table of Contents

- [Quick Start](#quick-start)
- [Chat Completions](#chat-completions)
- [Text Completions](#text-completions)
- [Embeddings](#embeddings)
- [Streaming Responses](#streaming-responses)
- [Error Handling](#error-handling)
- [Authentication](#authentication)

## Quick Start

### 1. Start the Gateway

```bash
# Using cargo
cargo run -- --config config/gateway.yaml

# Using Docker
docker run -p 8000:8000 -v ./config:/app/config majiayu000/litellm-rs

# Using docker-compose
docker-compose up -d
```

### 2. Test the Connection

```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "0.1.0"
}
```

### 3. List Available Models

```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/v1/models
```

## Chat Completions

### Basic Chat Completion

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "What is the capital of France?"
      }
    ]
  }'
```

### Chat with Temperature Control

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Write a creative story about a robot."
      }
    ],
    "temperature": 0.9,
    "max_tokens": 200
  }'
```

### Multi-turn Conversation

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful coding assistant."
      },
      {
        "role": "user",
        "content": "How do I create a REST API in Python?"
      },
      {
        "role": "assistant",
        "content": "You can create a REST API in Python using frameworks like Flask or FastAPI. Here'\''s a simple Flask example..."
      },
      {
        "role": "user",
        "content": "Can you show me a FastAPI example instead?"
      }
    ]
  }'
```

## Text Completions

### Basic Text Completion

```bash
curl -X POST http://localhost:8000/v1/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo-instruct",
    "prompt": "The benefits of renewable energy include",
    "max_tokens": 100,
    "temperature": 0.7
  }'
```

### Code Completion

```bash
curl -X POST http://localhost:8000/v1/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo-instruct",
    "prompt": "def fibonacci(n):\n    if n <= 1:\n        return n\n    else:\n        return",
    "max_tokens": 50,
    "temperature": 0.1,
    "stop": ["\n\n"]
  }'
```

## Embeddings

### Single Text Embedding

```bash
curl -X POST http://localhost:8000/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "text-embedding-ada-002",
    "input": "The quick brown fox jumps over the lazy dog"
  }'
```

### Multiple Text Embeddings

```bash
curl -X POST http://localhost:8000/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "text-embedding-ada-002",
    "input": [
      "Hello world",
      "How are you today?",
      "The weather is nice"
    ]
  }'
```

## Streaming Responses

### Streaming Chat Completion

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Tell me a long story about space exploration"
      }
    ],
    "stream": true
  }'
```

### Streaming Text Completion

```bash
curl -X POST http://localhost:8000/v1/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo-instruct",
    "prompt": "Write a detailed explanation of quantum computing",
    "max_tokens": 500,
    "stream": true
  }'
```

## Error Handling

### Invalid API Key

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

Expected response (401):
```json
{
  "error": {
    "type": "authentication_error",
    "code": "invalid_api_key",
    "message": "Invalid API key provided"
  }
}
```

### Invalid Model

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "non-existent-model",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

Expected response (400):
```json
{
  "error": {
    "type": "invalid_request_error",
    "code": "model_not_found",
    "message": "Model 'non-existent-model' not found"
  }
}
```

### Rate Limit Exceeded

Expected response (429):
```json
{
  "error": {
    "type": "rate_limit_error",
    "code": "rate_limit_exceeded",
    "message": "Rate limit exceeded. Try again in 60 seconds."
  }
}
```

## Authentication

### Using API Keys

```bash
# Bearer token format
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/v1/models

# Custom header format (if configured)
curl -H "X-API-Key: your-api-key" \
  http://localhost:8000/v1/models
```

### Using JWT Tokens

```bash
# First, obtain a JWT token (example login endpoint)
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-password"
  }'

# Use the JWT token
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  http://localhost:8000/v1/models
```

## Advanced Usage

### Custom Headers

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Request-ID: unique-request-id" \
  -H "X-User-ID: user-123" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

### Function Calling (if supported)

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "What'\''s the weather like in San Francisco?"
      }
    ],
    "functions": [
      {
        "name": "get_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            }
          },
          "required": ["location"]
        }
      }
    ]
  }'
```

## Testing and Development

### Health Check with Details

```bash
curl http://localhost:8000/health?detailed=true
```

### Metrics Endpoint

```bash
curl http://localhost:8000/metrics
```

### Debug Information

```bash
curl -H "Authorization: Bearer your-api-key" \
  http://localhost:8000/debug/info
```

## Next Steps

- Check out the [API Reference](../docs/api.md) for complete endpoint documentation
- See [Configuration Guide](../docs/configuration.md) for advanced configuration options
- Read the [Deployment Guide](../docs/deployment.md) for production deployment
- Explore more examples in the [examples directory](./)
