//! SDK使用示例

use litellm_rs::sdk::{self, *};

#[tokio::main]
async fn main() -> sdk::Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 LiteLLM SDK Example");

    // 创建配置
    let config = ConfigBuilder::new()
        .add_openai("openai", "your-api-key-here")
        .add_anthropic("anthropic", "your-anthropic-key-here")
        .default_provider("openai")
        .timeout(30)
        .max_retries(3)
        .build();

    // 创建客户端
    let client = LLMClient::new(config)?;

    // 准备消息
    let messages = vec![
        Message {
            role: Role::System,
            content: Some(Content::Text("You are a helpful assistant.".to_string())),
            name: None,
            tool_calls: None,
        },
        Message {
            role: Role::User,
            content: Some(Content::Text(
                "Hello! Tell me about Rust programming.".to_string(),
            )),
            name: None,
            tool_calls: None,
        },
    ];

    // 发送聊天请求
    println!("📤 Sending chat request...");
    let response = client.chat(messages).await?;

    // 显示响应
    println!("📨 Response received:");
    println!("ID: {}", response.id);
    println!("Model: {}", response.model);

    if let Some(choice) = response.choices.first() {
        if let Some(Content::Text(text)) = &choice.message.content {
            println!("Assistant: {}", text);
        }
    }

    println!("Usage: {} total tokens", response.usage.total_tokens);

    // 列出可用providers
    println!("\n📋 Available providers:");
    for provider in client.list_providers() {
        println!("  - {}", provider);
    }

    println!("\n✅ Example completed successfully!");

    Ok(())
}
