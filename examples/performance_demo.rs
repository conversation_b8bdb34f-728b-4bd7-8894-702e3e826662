//! Performance demonstration of litellm-rs optimizations
//!
//! This example demonstrates the performance improvements achieved through
//! various optimizations in the litellm-rs library.

use litellm_rs::core::cache_manager::{CacheConfig, CacheManager};
use litellm_rs::core::models::openai::*;
use litellm_rs::utils::string_pool::{StringPool, intern_string};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 LiteLLM-RS Performance Demonstration");
    println!("======================================\n");

    // Demo 1: String Pool Performance
    demo_string_pool_performance().await;

    // Demo 2: Cache Performance
    demo_cache_performance().await?;

    // Demo 3: Concurrent Operations
    demo_concurrent_performance().await?;

    println!("\n✅ All performance demonstrations completed successfully!");
    Ok(())
}

/// Demonstrate string pool performance improvements
async fn demo_string_pool_performance() {
    println!("📊 String Pool Performance Demo");
    println!("-------------------------------");

    let iterations = 10000;

    // Test 1: Regular string operations
    let start = Instant::now();
    let mut regular_strings = Vec::new();
    for i in 0..iterations {
        let s = format!("model_name_{}", i % 100); // Simulate repeated model names
        regular_strings.push(s);
    }
    let regular_time = start.elapsed();

    // Test 2: String pool operations
    let pool = StringPool::new();
    let start = Instant::now();
    let mut pooled_strings = Vec::new();
    for i in 0..iterations {
        let s = format!("model_name_{}", i % 100);
        pooled_strings.push(pool.intern(&s));
    }
    let pooled_time = start.elapsed();

    // Test 3: Global string interning
    let start = Instant::now();
    let mut interned_strings = Vec::new();
    for i in 0..iterations {
        let s = format!("model_name_{}", i % 100);
        interned_strings.push(intern_string(&s));
    }
    let interned_time = start.elapsed();

    println!("  Regular strings ({} ops): {:?}", iterations, regular_time);
    println!("  Pooled strings ({} ops):  {:?}", iterations, pooled_time);
    println!(
        "  Interned strings ({} ops): {:?}",
        iterations, interned_time
    );
    println!("  Pool contains {} unique strings", pool.len());

    let improvement = regular_time.as_nanos() as f64 / pooled_time.as_nanos() as f64;
    println!("  🎯 Performance improvement: {:.2}x faster\n", improvement);
}

/// Demonstrate cache performance
async fn demo_cache_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("💾 Cache Performance Demo");
    println!("-------------------------");

    let config = CacheConfig {
        max_entries: 1000,
        default_ttl: Duration::from_secs(3600),
        enable_semantic: false,
        similarity_threshold: 0.95,
        min_prompt_length: 10,
        enable_compression: false,
    };

    let cache = CacheManager::new(config)?;
    let iterations = 1000;

    // Prepare test data
    let mut keys = Vec::new();
    let mut responses = Vec::new();

    for i in 0..iterations {
        let key = litellm_rs::core::cache_manager::CacheKey {
            model: intern_string("gpt-4"),
            request_hash: i as u64,
            user_id: None,
        };

        let response = ChatCompletionResponse {
            id: format!("demo_{}", i),
            object: "chat.completion".to_string(),
            created: 1234567890,
            model: "gpt-4".to_string(),
            choices: vec![],
            usage: None,
            system_fingerprint: None,
        };

        keys.push(key);
        responses.push(response);
    }

    // Test cache write performance
    let start = Instant::now();
    for (key, response) in keys.iter().zip(responses.iter()) {
        cache.put(key.clone(), response.clone()).await?;
    }
    let write_time = start.elapsed();

    // Test cache read performance (should be much faster due to caching)
    let start = Instant::now();
    let mut hit_count = 0;
    for key in &keys {
        if cache.get(key).await?.is_some() {
            hit_count += 1;
        }
    }
    let read_time = start.elapsed();

    println!("  Cache writes ({} ops): {:?}", iterations, write_time);
    println!("  Cache reads ({} ops):  {:?}", iterations, read_time);
    println!(
        "  Cache hit rate: {:.1}%",
        (hit_count as f64 / iterations as f64) * 100.0
    );

    let stats = cache.stats();
    println!("  Cache statistics: {:?}", stats);

    let ops_per_sec = iterations as f64 / read_time.as_secs_f64();
    println!("  🎯 Read performance: {:.0} ops/second\n", ops_per_sec);

    Ok(())
}

/// Demonstrate concurrent performance
async fn demo_concurrent_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔄 Concurrent Performance Demo");
    println!("------------------------------");

    let config = CacheConfig::default();
    let cache = Arc::new(CacheManager::new(config)?);
    let num_tasks = 100;
    let ops_per_task = 10;

    // Test concurrent cache operations
    let start = Instant::now();
    let mut handles = Vec::new();

    for task_id in 0..num_tasks {
        let cache = cache.clone();
        let handle = tokio::spawn(async move {
            for op_id in 0..ops_per_task {
                let key = litellm_rs::core::cache_manager::CacheKey {
                    model: intern_string("gpt-4"),
                    request_hash: (task_id * ops_per_task + op_id) as u64,
                    user_id: None,
                };

                let response = ChatCompletionResponse {
                    id: format!("concurrent_{}_{}", task_id, op_id),
                    object: "chat.completion".to_string(),
                    created: 1234567890,
                    model: "gpt-4".to_string(),
                    choices: vec![],
                    usage: None,
                    system_fingerprint: None,
                };

                // Perform both write and read operations
                cache.put(key.clone(), response).await.unwrap();
                cache.get(&key).await.unwrap();
            }
        });
        handles.push(handle);
    }

    // Wait for all tasks to complete
    for handle in handles {
        handle.await?;
    }

    let total_time = start.elapsed();
    let total_ops = num_tasks * ops_per_task * 2; // 2 ops per iteration (put + get)
    let ops_per_sec = total_ops as f64 / total_time.as_secs_f64();

    println!("  Concurrent tasks: {}", num_tasks);
    println!("  Operations per task: {} (put + get)", ops_per_task * 2);
    println!("  Total operations: {}", total_ops);
    println!("  Total time: {:?}", total_time);
    println!("  🎯 Concurrent performance: {:.0} ops/second", ops_per_sec);

    let stats = cache.stats();
    println!("  Final cache statistics: {:?}\n", stats);

    Ok(())
}

/// Helper function to format duration nicely
#[allow(dead_code)]
fn format_duration(duration: Duration) -> String {
    let nanos = duration.as_nanos();
    if nanos < 1_000 {
        format!("{}ns", nanos)
    } else if nanos < 1_000_000 {
        format!("{:.2}μs", nanos as f64 / 1_000.0)
    } else if nanos < 1_000_000_000 {
        format!("{:.2}ms", nanos as f64 / 1_000_000.0)
    } else {
        format!("{:.2}s", nanos as f64 / 1_000_000_000.0)
    }
}
