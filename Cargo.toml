[package]
name = "litellm-rs"
version = "0.1.1"
edition = "2024"
rust-version = "1.87"
authors = ["LiteLLM-RS Contributors"]
description = "A high-performance AI Gateway written in Rust, providing OpenAI-compatible APIs with intelligent routing, load balancing, and enterprise features"
documentation = "https://docs.rs/litellm-rs"
homepage = "https://github.com/majiayu000/litellm-rs"
repository = "https://github.com/majiayu000/litellm-rs"
license = "MIT"
readme = "README.md"
keywords = ["ai", "gateway", "llm", "openai", "anthropic"]
categories = ["web-programming", "api-bindings", "network-programming"]
include = [
    "src/**/*",
    "config/*.example",
    "config/*.template",
    "migrations/**/*",
    "build.rs",
    "Cargo.toml",
    "README.md",
    "LICENSE",
    "LICENSE-LITELLM",
    "CHANGELOG.md"
]
default-run = "gateway"

# 二进制文件
[[bin]]
name = "gateway"
path = "src/main.rs"

[[bin]]
name = "google-gateway"
path = "src/bin/google_gateway.rs"

[dependencies]
# Web 框架和异步运行时
actix-web = { version = "4.4", features = ["rustls"] }
actix-cors = "0.7"
actix-ws = "0.2"
actix-multipart = "0.6"
actix-files = "0.6"
tokio = { version = "1.35", features = ["full"] }
tokio-stream = "0.1"
futures = "0.3"
async-trait = "0.1"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "stream", "rustls-tls"], default-features = false }
hyper = { version = "0.14", features = ["full"] }

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 数据库和存储
sea-orm = { version = "0.12", features = ["sqlx-postgres", "sqlx-sqlite", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid", "with-json"] }
sea-orm-migration = "0.12"
redis = { version = "0.24", features = ["tokio-comp", "cluster", "streams"], optional = true }

# 缓存
moka = { version = "0.12", features = ["future"] }
lru = "0.12"

# 对象存储 (S3 兼容)
object_store = { version = "0.8", features = ["aws", "gcp", "azure"], optional = true }
aws-sdk-s3 = { version = "1.96", optional = true }

# 向量数据库客户端
qdrant-client = { version = "1.7", optional = true }

# 认证和安全
jsonwebtoken = "9.2"
argon2 = "0.5"
uuid = { version = "1.6", features = ["v4", "serde"] }
sha2 = "0.10"
hmac = "0.12"
rand = "0.8"
regex = "1.10"
hex = "0.4"
url = "2.5"
base64 = "0.21"

# 监控和追踪
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-actix-web = "0.7"
opentelemetry = { version = "0.21", optional = true }
opentelemetry-jaeger = { version = "0.20", optional = true }
prometheus = { version = "0.13", optional = true }

# 中间件和工具
governor = "0.6"

# 配置管理
config = "0.14"
clap = { version = "4.4", features = ["derive", "env"] }
dotenvy = "0.15"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 并发和同步
parking_lot = "0.12"
arc-swap = "1.6"
dashmap = "5.5"
once_cell = "1.19"

# WebSocket 支持
tungstenite = { version = "0.21", optional = true }

# 日志
log = "0.4"
env_logger = "0.11"
num_cpus = "1.17"

# 开发依赖
[dev-dependencies]
tokio-test = "0.4.4"
mockall = "0.13.1"
wiremock = "0.6.4"
criterion = { version = "0.6.0", features = ["html_reports"] }
tempfile = "3.20.0"

# 库
[lib]
name = "litellm_rs"
path = "src/lib.rs"

# 编译优化
[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0

# 功能特性
[features]
default = ["sqlite", "redis", "metrics", "tracing"]

# 存储后端
postgres = ["sea-orm/sqlx-postgres"]
sqlite = ["sea-orm/sqlx-sqlite"]
redis = ["dep:redis"]
s3 = ["dep:object_store", "dep:aws-sdk-s3"]

# 监控和可观测性
metrics = ["dep:prometheus"]
tracing = ["dep:opentelemetry", "dep:opentelemetry-jaeger"]

# 高级功能
vector-db = ["dep:qdrant-client"]
websockets = ["dep:tungstenite"]
analytics = ["metrics"]
enterprise = ["analytics", "vector-db"]

# 完整功能集
full = ["postgres", "redis", "s3", "metrics", "tracing", "vector-db", "websockets", "analytics"]

# 元数据
[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[package.metadata.playground]
features = ["full"]
