# Rust LiteLLM Gateway Configuration Example
# Copy this file to gateway.yaml and customize for your environment

# Server Configuration
server:
  # Binding configuration
  host: "0.0.0.0"                    # Bind to all interfaces (use "127.0.0.1" for localhost only)
  port: 8000                         # HTTP port
  workers: 4                         # Number of worker threads (0 = auto-detect CPU cores)
  
  # Performance settings
  max_connections: 1000              # Maximum concurrent connections
  keep_alive: 75                     # Keep-alive timeout in seconds
  timeout: 30                        # Request timeout in seconds
  max_body_size: 10485760           # Maximum request body size (10MB)
  
  # TLS configuration (optional)
  tls:
    enabled: false                   # Enable HTTPS
    cert_file: "/path/to/cert.pem"   # Certificate file path
    key_file: "/path/to/key.pem"     # Private key file path
    
  # CORS settings
  cors:
    enabled: true                    # Enable CORS
    allowed_origins: ["*"]           # Allowed origins (* for all)
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
    max_age: 3600                    # Preflight cache duration

# Provider Configuration
providers:
  # OpenAI Provider
  - name: "openai-primary"
    provider_type: "openai"
    enabled: true
    weight: 100                      # Routing weight (higher = more traffic)
    priority: 1                      # Priority level (lower = higher priority)
    
    config:
      api_key: "${OPENAI_API_KEY}"   # Use environment variable
      base_url: "https://api.openai.com/v1"
      organization: "${OPENAI_ORG_ID}"  # Optional organization ID
      
    models:
      - "gpt-4"
      - "gpt-4-turbo"
      - "gpt-3.5-turbo"
      - "text-embedding-ada-002"
      
    limits:
      max_requests_per_minute: 1000
      max_tokens_per_minute: 100000
      
    health_check:
      enabled: true
      interval: 30                   # Health check interval in seconds
      timeout: 10                    # Health check timeout
      retries: 3                     # Retry attempts before marking unhealthy
      
  # Anthropic Provider
  - name: "anthropic-backup"
    provider_type: "anthropic"
    enabled: true
    weight: 80
    priority: 2
    
    config:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      
    models:
      - "claude-3-opus-20240229"
      - "claude-3-sonnet-20240229"
      - "claude-3-haiku-20240307"
      
    limits:
      max_requests_per_minute: 500
      max_tokens_per_minute: 50000
      
  # Azure OpenAI Provider
  - name: "azure-openai"
    provider_type: "azure"
    enabled: false                   # Disabled by default
    weight: 90
    
    config:
      api_key: "${AZURE_OPENAI_API_KEY}"
      endpoint: "${AZURE_OPENAI_ENDPOINT}"
      api_version: "2024-02-01"
      
    models:
      - name: "gpt-4"
        deployment_name: "gpt-4-deployment"
      - name: "gpt-35-turbo"
        deployment_name: "gpt-35-turbo-deployment"
        
  # Google AI Provider
  - name: "google-ai"
    provider_type: "google"
    enabled: false
    weight: 70
    
    config:
      api_key: "${GOOGLE_AI_API_KEY}"
      base_url: "https://generativelanguage.googleapis.com"
      
    models:
      - "gemini-pro"
      - "gemini-pro-vision"
      
  # Cohere Provider
  - name: "cohere"
    provider_type: "cohere"
    enabled: false
    weight: 60
    
    config:
      api_key: "${COHERE_API_KEY}"
      base_url: "https://api.cohere.ai"
      
    models:
      - "command"
      - "command-light"

# Router Configuration
router:
  # Routing strategy
  strategy:
    type: "least_latency"             # Options: round_robin, least_latency, least_cost, random, weighted, priority, ab_test, custom
  
  # Health monitoring
  health_check_interval: 30           # Global health check interval
  unhealthy_threshold: 3              # Failed checks before marking unhealthy
  healthy_threshold: 2                # Successful checks before marking healthy
  
  # Retry configuration
  retry_attempts: 3                   # Maximum retry attempts
  retry_delay: 1000                   # Initial retry delay in milliseconds
  retry_backoff: 2.0                  # Backoff multiplier for retries
  
  # Timeout settings
  provider_timeout: 30                # Individual provider timeout
  total_timeout: 60                   # Total request timeout including retries
  
  # Load balancing
  load_balancing:
    algorithm: "weighted_round_robin"
    health_weight: 0.3                # Weight given to health scores
    latency_weight: 0.4               # Weight given to latency scores
    cost_weight: 0.3                  # Weight given to cost scores
    
  # Failover settings
  failover:
    enabled: true
    max_failures: 5                   # Max failures before circuit breaker opens
    recovery_time: 60                 # Seconds before attempting recovery

# Authentication Configuration
auth:
  # JWT Configuration
  jwt:
    enabled: true
    secret: "${JWT_SECRET}"           # JWT signing secret (required)
    algorithm: "HS256"                # Signing algorithm
    expiration: 3600                  # Token expiration in seconds (1 hour)
    refresh_enabled: true             # Enable refresh tokens
    refresh_expiration: 86400         # Refresh token expiration (24 hours)
    
  # API Key Configuration
  api_key:
    enabled: true
    header: "Authorization"           # Header name for API key
    prefix: "Bearer "                 # Optional prefix (e.g., "Bearer ")
    
  # Session Configuration
  session:
    enabled: true
    timeout: 1800                     # Session timeout in seconds (30 minutes)
    cleanup_interval: 300             # Session cleanup interval (5 minutes)
    
  # RBAC Configuration
  rbac:
    enabled: true
    default_role: "user"              # Default role for new users
    admin_role: "admin"               # Admin role name
    
    roles:
      - name: "admin"
        permissions: ["*"]            # All permissions
        
      - name: "user"
        permissions:
          - "chat:create"
          - "completion:create"
          - "embedding:create"
          - "model:list"
          
      - name: "readonly"
        permissions:
          - "model:list"
          - "health:check"

# Storage Configuration
storage:
  # Database Configuration (PostgreSQL)
  database:
    url: "${DATABASE_URL}"            # PostgreSQL connection string
    max_connections: 10               # Connection pool size
    min_connections: 1                # Minimum connections in pool
    connection_timeout: 30            # Connection timeout in seconds
    idle_timeout: 600                 # Idle connection timeout (10 minutes)
    max_lifetime: 3600                # Connection max lifetime (1 hour)
    
    # Migration settings
    auto_migrate: true                # Run migrations on startup
    migration_path: "migrations/"     # Migration files directory
    
  # Redis Configuration
  redis:
    url: "${REDIS_URL}"               # Redis connection string
    max_connections: 10               # Connection pool size
    connection_timeout: 5             # Connection timeout
    command_timeout: 5                # Command timeout
    
    # Cluster configuration (optional)
    cluster:
      enabled: false
      nodes:
        - "redis://node1:6379"
        - "redis://node2:6379"
        - "redis://node3:6379"
        
  # File Storage Configuration
  files:
    backend: "local"                  # Options: local, s3, gcs, azure
    
    # Local storage
    local:
      path: "./data/files"
      
    # S3 configuration
    s3:
      bucket: "${S3_BUCKET}"
      region: "${AWS_REGION}"
      access_key: "${AWS_ACCESS_KEY_ID}"
      secret_key: "${AWS_SECRET_ACCESS_KEY}"
      endpoint: "${S3_ENDPOINT}"      # Optional for S3-compatible services
      
  # Vector Database Configuration (optional)
  vector:
    enabled: false
    backend: "qdrant"                 # Options: qdrant, pinecone, weaviate
    
    qdrant:
      url: "${QDRANT_URL}"
      api_key: "${QDRANT_API_KEY}"
      collection: "embeddings"

# Caching Configuration
caching:
  # Memory cache
  memory:
    enabled: true
    max_size: 1000                    # Maximum number of entries
    ttl: 300                          # Time to live in seconds (5 minutes)
    
  # Redis cache
  redis:
    enabled: true
    ttl: 3600                         # Default TTL (1 hour)
    key_prefix: "gateway:"            # Key prefix for namespacing
    
  # Response caching
  response:
    enabled: true
    ttl: 300                          # Response cache TTL (5 minutes)
    vary_by:                          # Cache key variations
      - "user_id"
      - "model"
      - "temperature"
      
  # Semantic caching (requires vector database)
  semantic:
    enabled: false
    similarity_threshold: 0.95        # Similarity threshold for cache hits
    embedding_model: "text-embedding-ada-002"

# Monitoring Configuration
monitoring:
  # Metrics
  metrics:
    enabled: true
    port: 9090                        # Prometheus metrics port
    path: "/metrics"                  # Metrics endpoint path
    
  # Health checks
  health:
    enabled: true
    path: "/health"                   # Health check endpoint
    
  # Logging
  logging:
    level: "info"                     # Log level: trace, debug, info, warn, error
    format: "json"                    # Format: json, text
    output: "stdout"                  # Output: stdout, stderr, file
    file: "/var/log/gateway.log"      # Log file path (if output is file)
    
  # Distributed tracing
  tracing:
    enabled: false
    endpoint: "${JAEGER_ENDPOINT}"    # Jaeger collector endpoint
    service_name: "litellm-rs"
    sample_rate: 0.1                  # Sampling rate (0.0 to 1.0)
    
  # Alerting
  alerting:
    enabled: false
    
    # Slack integration
    slack:
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#alerts"
      username: "Gateway Bot"
      
    # Email integration
    email:
      smtp_host: "${SMTP_HOST}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from: "<EMAIL>"
      to: ["<EMAIL>"]
      
    # Alert rules
    rules:
      - name: "high_error_rate"
        condition: "error_rate > 0.05"
        duration: "5m"
        severity: "warning"
        
      - name: "provider_down"
        condition: "provider_health == 0"
        duration: "1m"
        severity: "critical"
