//! 简化的LLM客户端实现

use crate::sdk::{config::ClientConfig, errors::*, types::*};

/// 简化的LLM客户端
#[derive(Debug, Clone)]
pub struct SimpleLLMClient {
    config: ClientConfig,
}

impl SimpleLLMClient {
    /// 创建新的LLM客户端
    pub fn new(config: ClientConfig) -> Result<Self> {
        if config.providers.is_empty() {
            return Err(SDKError::ConfigError("No providers configured".to_string()));
        }

        Ok(Self { config })
    }

    /// 创建新的LLM客户端（异步版本，用于真实provider集成）
    pub async fn new_async(config: ClientConfig) -> Result<Self> {
        if config.providers.is_empty() {
            return Err(SDKError::ConfigError("No providers configured".to_string()));
        }

        // 这里可以初始化真实的provider registry
        // let _registry = crate::sdk::providers::ProviderRegistry::new(&config).await?;

        Ok(Self { config })
    }

    /// 发送聊天消息
    pub async fn chat(&self, _messages: Vec<Message>) -> Result<ChatResponse> {
        // 简化实现 - 返回模拟响应
        Ok(ChatResponse {
            id: uuid::Uuid::new_v4().to_string(),
            model: "gpt-3.5-turbo".to_string(),
            choices: vec![ChatChoice {
                index: 0,
                message: Message {
                    role: Role::Assistant,
                    content: Some(Content::Text(
                        "This is a simulated response from the SDK.".to_string(),
                    )),
                    name: None,
                    tool_calls: None,
                },
                finish_reason: Some("stop".to_string()),
            }],
            usage: Usage {
                prompt_tokens: 10,
                completion_tokens: 15,
                total_tokens: 25,
            },
            created: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }

    /// 列出可用的providers
    pub fn list_providers(&self) -> Vec<String> {
        self.config.providers.iter().map(|p| p.id.clone()).collect()
    }

    /// 获取配置
    pub fn config(&self) -> &ClientConfig {
        &self.config
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sdk::config::{ConfigBuilder, ProviderType};

    #[tokio::test]
    async fn test_simple_client() {
        let config = ConfigBuilder::new()
            .add_provider(crate::sdk::config::ProviderConfig {
                id: "test".to_string(),
                provider_type: ProviderType::OpenAI,
                name: "Test Provider".to_string(),
                api_key: "test-key".to_string(),
                base_url: None,
                models: vec!["gpt-3.5-turbo".to_string()],
                enabled: true,
                weight: 1.0,
                rate_limit_rpm: Some(1000),
                rate_limit_tpm: Some(10000),
                settings: std::collections::HashMap::new(),
            })
            .build();

        let client = SimpleLLMClient::new(config).unwrap();

        let messages = vec![Message {
            role: Role::User,
            content: Some(Content::Text("Hello".to_string())),
            name: None,
            tool_calls: None,
        }];

        let response = client.chat(messages).await.unwrap();
        assert!(!response.choices.is_empty());
        assert_eq!(response.choices[0].message.role, Role::Assistant);
    }
}
