//! SDK data types

use serde::{Deserialize, Serialize};

/// 消息角色
#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Role {
    /// 系统消息
    System,
    /// 用户消息
    User,
    /// 助手消息
    Assistant,
    /// 工具消息
    Tool,
}

/// 消息内容类型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum Content {
    /// 纯文本内容
    Text(String),
    /// 多模态内容
    Multimodal(Vec<ContentPart>),
}

/// 内容部分
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ContentPart {
    /// 文本内容
    #[serde(rename = "text")]
    Text {
        /// 文本字符串
        text: String,
    },
    /// 图片内容
    #[serde(rename = "image_url")]
    Image {
        /// 图片URL信息
        image_url: ImageUrl,
    },
    /// 音频内容
    #[serde(rename = "audio")]
    Audio {
        /// 音频数据
        audio: AudioData,
    },
}

/// 图片URL
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageUrl {
    /// 图片URL或base64数据
    pub url: String,
    /// 图片详细度
    pub detail: Option<String>,
}

/// 音频数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioData {
    /// 音频数据或URL
    pub data: String,
    /// 音频格式
    pub format: Option<String>,
}

/// 聊天消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 消息角色
    pub role: Role,
    /// 消息内容
    pub content: Option<Content>,
    /// 消息名称
    pub name: Option<String>,
    /// 工具调用
    pub tool_calls: Option<Vec<ToolCall>>,
}

/// 工具调用
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    /// 调用ID
    pub id: String,
    /// 工具类型
    #[serde(rename = "type")]
    pub tool_type: String,
    /// 函数调用
    pub function: Function,
}

/// 函数定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Function {
    /// 函数名称
    pub name: String,
    /// 函数描述
    pub description: Option<String>,
    /// 函数参数Schema
    pub parameters: serde_json::Value,
    /// 函数参数（用于调用）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub arguments: Option<String>,
}

/// 工具定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tool {
    /// 工具类型
    #[serde(rename = "type")]
    pub tool_type: String,
    /// 函数定义
    pub function: Function,
}

/// 工具选择
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ToolChoice {
    /// 不使用工具
    None,
    /// 自动选择
    Auto,
    /// 必须使用工具
    Required,
    /// 指定函数
    Function {
        /// 函数名称
        name: String,
    },
}

/// 聊天请求
#[derive(Debug, Clone)]
pub struct ChatRequest {
    /// 模型名称
    pub model: String,
    /// 消息列表
    pub messages: Vec<Message>,
    /// 请求选项
    pub options: ChatOptions,
}

/// 聊天选项
#[derive(Debug, Clone, Default)]
pub struct ChatOptions {
    /// 温度参数
    pub temperature: Option<f32>,
    /// 最大token数
    pub max_tokens: Option<u32>,
    /// Top-p参数
    pub top_p: Option<f32>,
    /// 频率惩罚
    pub frequency_penalty: Option<f32>,
    /// 存在惩罚
    pub presence_penalty: Option<f32>,
    /// 停止序列
    pub stop: Option<Vec<String>>,
    /// 是否流式响应
    pub stream: bool,
    /// 工具列表
    pub tools: Option<Vec<Tool>>,
    /// 工具选择
    pub tool_choice: Option<ToolChoice>,
}



/// 聊天响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatResponse {
    /// 响应ID
    pub id: String,
    /// 使用的模型
    pub model: String,
    /// 选择列表
    pub choices: Vec<ChatChoice>,
    /// 使用统计
    pub usage: Usage,
    /// 创建时间
    pub created: u64,
}

/// 聊天选择
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatChoice {
    /// 选择索引
    pub index: u32,
    /// 消息
    pub message: Message,
    /// 结束原因
    pub finish_reason: Option<String>,
}

/// 流式响应块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatChunk {
    /// 响应ID
    pub id: String,
    /// 使用的模型
    pub model: String,
    /// 选择列表
    pub choices: Vec<ChunkChoice>,
}

/// 流式选择
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkChoice {
    /// 选择索引
    pub index: u32,
    /// 增量消息
    pub delta: MessageDelta,
    /// 结束原因
    pub finish_reason: Option<String>,
}

/// 增量消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageDelta {
    /// 消息角色
    pub role: Option<Role>,
    /// 消息内容
    pub content: Option<String>,
    /// 工具调用
    pub tool_calls: Option<Vec<ToolCall>>,
}

/// 使用统计
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Usage {
    /// 提示token数
    pub prompt_tokens: u32,
    /// 完成token数
    pub completion_tokens: u32,
    /// 总token数
    pub total_tokens: u32,
}

/// 成本信息
#[derive(Debug, Clone)]
pub struct Cost {
    /// 成本金额
    pub amount: f64,
    /// 货币类型
    pub currency: String,
    /// 成本分解
    pub breakdown: CostBreakdown,
}

/// 成本分解
#[derive(Debug, Clone)]
pub struct CostBreakdown {
    /// 输入成本
    pub input_cost: f64,
    /// 输出成本
    pub output_cost: f64,
    /// 总成本
    pub total_cost: f64,
}
