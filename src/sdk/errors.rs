//! SDK错误类型

use thiserror::Error;

/// SDK错误类型
#[derive(Error, Debug)]
pub enum SDKError {
    /// Provider未找到
    #[error("Provider not found: {0}")]
    ProviderNotFound(String),

    /// 没有默认Provider
    #[error("No default provider configured")]
    NoDefaultProvider,

    /// Provider错误
    #[error("Provider error: {0}")]
    ProviderError(String),

    /// 配置错误
    #[error("Configuration error: {0}")]
    ConfigError(String),

    /// 网络错误
    #[error("Network error: {0}")]
    NetworkError(String),

    /// 认证错误
    #[error("Authentication error: {0}")]
    AuthError(String),

    /// 速率限制错误
    #[error("Rate limit exceeded: {0}")]
    RateLimitError(String),

    /// 模型未找到
    #[error("Model not found: {0}")]
    ModelNotFound(String),

    /// 功能不支持
    #[error("Feature not supported: {0}")]
    NotSupported(String),

    /// 不支持的Provider
    #[error("Unsupported provider: {0}")]
    UnsupportedProvider(String),

    /// 序列化错误
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    /// HTTP错误
    #[error("HTTP error: {0}")]
    HttpError(#[from] reqwest::Error),

    /// 无效请求
    #[error("Invalid request: {0}")]
    InvalidRequest(String),

    /// 内部错误
    #[error("Internal error: {0}")]
    Internal(String),
}

/// 从网关错误转换
impl From<crate::utils::error::GatewayError> for SDKError {
    fn from(error: crate::utils::error::GatewayError) -> Self {
        match error {
            crate::utils::error::GatewayError::Unauthorized(msg) => SDKError::AuthError(msg),
            crate::utils::error::GatewayError::NotFound(msg) => SDKError::ModelNotFound(msg),
            crate::utils::error::GatewayError::BadRequest(msg) => SDKError::InvalidRequest(msg),
            crate::utils::error::GatewayError::RateLimit(msg) => SDKError::RateLimitError(msg),
            crate::utils::error::GatewayError::ServiceUnavailable(msg) => {
                SDKError::ProviderError(msg)
            }
            crate::utils::error::GatewayError::Internal(msg) => SDKError::Internal(msg),
            crate::utils::error::GatewayError::Network(msg) => SDKError::NetworkError(msg),
            crate::utils::error::GatewayError::Validation(msg) => SDKError::InvalidRequest(msg),
            crate::utils::error::GatewayError::Parsing(msg) => SDKError::Internal(msg),
            // 处理其他所有变体
            _ => SDKError::Internal(error.to_string()),
        }
    }
}

/// 从Provider错误转换  
impl From<crate::core::providers::ProviderError> for SDKError {
    fn from(error: crate::core::providers::ProviderError) -> Self {
        match error {
            crate::core::providers::ProviderError::Authentication(msg) => SDKError::AuthError(msg),
            crate::core::providers::ProviderError::RateLimit(msg) => SDKError::RateLimitError(msg),
            crate::core::providers::ProviderError::RateLimited(msg) => {
                SDKError::RateLimitError(msg)
            }
            crate::core::providers::ProviderError::ModelNotFound(msg) => {
                SDKError::ModelNotFound(msg)
            }
            crate::core::providers::ProviderError::InvalidRequest(msg) => {
                SDKError::InvalidRequest(msg)
            }
            crate::core::providers::ProviderError::Unavailable(msg) => SDKError::ProviderError(msg),
            crate::core::providers::ProviderError::Network(msg) => SDKError::NetworkError(msg),
            crate::core::providers::ProviderError::Parsing(msg) => SDKError::Internal(msg),
            crate::core::providers::ProviderError::Timeout(msg) => SDKError::NetworkError(msg),
            crate::core::providers::ProviderError::Other(msg) => SDKError::Internal(msg),
            crate::core::providers::ProviderError::Unknown(msg) => SDKError::Internal(msg),
        }
    }
}

/// SDK结果类型
pub type Result<T> = std::result::Result<T, SDKError>;

impl SDKError {
    /// 检查错误是否可重试
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            SDKError::NetworkError(_) | SDKError::RateLimitError(_) | SDKError::ProviderError(_)
        )
    }

    /// 检查是否是认证相关错误
    pub fn is_auth_error(&self) -> bool {
        matches!(self, SDKError::AuthError(_))
    }

    /// 检查是否是配置相关错误
    pub fn is_config_error(&self) -> bool {
        matches!(
            self,
            SDKError::ConfigError(_) | SDKError::ProviderNotFound(_) | SDKError::NoDefaultProvider
        )
    }
}
