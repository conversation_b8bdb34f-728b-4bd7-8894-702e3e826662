//! Configuration management for the Gateway
//!
//! This module handles loading, validation, and management of all gateway configuration.

pub mod builder;
pub mod models;
// pub mod loader;
// pub mod validation;

pub use models::*;
// pub use builder::*;  // Commented out until actually used
// pub use loader::*;

use crate::utils::error::{GatewayError, Result};
use std::path::Path;
use tracing::{debug, info};

/// Main configuration struct for the Gateway
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
pub struct Config {
    /// Gateway configuration
    pub gateway: GatewayConfig,
}

#[allow(dead_code)]
impl Config {
    /// Load configuration from file
    pub async fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        info!("Loading configuration from: {:?}", path);

        let content = tokio::fs::read_to_string(path)
            .await
            .map_err(|e| GatewayError::Config(format!("Failed to read config file: {}", e)))?;

        let gateway: GatewayConfig = serde_yaml::from_str(&content)
            .map_err(|e| GatewayError::Config(format!("Failed to parse config: {}", e)))?;

        let config = Self { gateway };

