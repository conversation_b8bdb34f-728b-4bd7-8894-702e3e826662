//! HTTP server implementation
//!
//! This module provides the HTTP server and routing functionality.

pub mod middleware;
pub mod routes;

use crate::config::{Config, ServerConfig};
use crate::utils::error::{GatewayError, Result};
use actix_cors::Cors;
use actix_web::{
    App, HttpResponse, HttpServer as ActixHttpServer,
    middleware::{DefaultHeaders, Logger},
    web,
};
use chrono;
use serde_json::json;
use std::sync::Arc;

use tracing::info;

/// HTTP server state shared across handlers
///
/// This struct contains shared resources that need to be accessed across
/// multiple request handlers. All fields are wrapped in Arc for efficient
/// sharing across threads.
#[derive(Clone)]
#[allow(dead_code)]
pub struct AppState {
    /// Gateway configuration (shared read-only)
    pub config: Arc<Config>,
    /// Authentication system
    pub auth: Arc<crate::auth::AuthSystem>,
    /// Request router
    pub router: Arc<crate::core::router::Router>,
    /// Storage layer
    pub storage: Arc<crate::storage::StorageLayer>,
}

impl AppState {
    /// Create a new AppState with shared resources
    pub fn new(
        config: Config,
        auth: crate::auth::AuthSystem,
        router: crate::core::router::Router,
        storage: crate::storage::StorageLayer,
    ) -> Self {
        Self {
            config: Arc::new(config),
            auth: Arc::new(auth),
            router: Arc::new(router),
            storage: Arc::new(storage),
        }
    }
}

/// HTTP server
#[allow(dead_code)]
pub struct HttpServer {
    /// Server configuration
    config: ServerConfig,
    /// Application state
    state: AppState,
}

#[allow(dead_code)]
impl HttpServer {
    /// Create a new HTTP server
    pub async fn new(config: &Config) -> Result<Self> {
        info!("Creating HTTP server");

        // Create storage layer
        let storage = crate::storage::StorageLayer::new(&config.gateway.storage).await?;

        // Create auth system
        let auth =
            crate::auth::AuthSystem::new(&config.gateway.auth, Arc::new(storage.clone())).await?;

        // Create router
        let router = crate::core::router::Router::new(
            config.gateway.providers.clone(),
            Arc::new(storage.clone()),
            crate::core::router::RoutingStrategy::RoundRobin,
        )
        .await?;

        // Create shared state using the builder method
        let state = AppState::new(config.clone(), auth, router, storage);

        Ok(Self {
            config: config.gateway.server.clone(),
            state,
        })
    }

    /// Create the Actix-web application
    fn create_app(
        state: web::Data<AppState>,
    ) -> App<
        impl actix_web::dev::ServiceFactory<
            actix_web::dev::ServiceRequest,
            Config = (),
            Response = actix_web::dev::ServiceResponse<impl actix_web::body::MessageBody>,
            Error = actix_web::Error,
            InitError = (),
        >,
    > {
        info!("Setting up routes and middleware");

        let cors_config = &state.config.gateway.server.cors;
        let mut cors = Cors::default();

        // Configure CORS based on settings
        if cors_config.enabled {
            if cors_config.allows_all_origins() {
                cors = cors.allow_any_origin();
                cors_config.validate().unwrap_or_else(|e| {
                    eprintln!("⚠️  CORS Configuration Warning: {}", e);
                });
            } else {
                for origin in &cors_config.allowed_origins {
                    cors = cors.allowed_origin(origin);
                }
            }

            // Convert method strings to actix methods
            let methods: Vec<actix_web::http::Method> = cors_config
                .allowed_methods
                .iter()
                .filter_map(|m| m.parse().ok())
                .collect();
            if !methods.is_empty() {
                cors = cors.allowed_methods(methods);
            }

            // Convert header strings
            let headers: Vec<actix_web::http::header::HeaderName> = cors_config
                .allowed_headers
                .iter()
                .filter_map(|h| h.parse().ok())
                .collect();
            if !headers.is_empty() {
                cors = cors.allowed_headers(headers);
            }

            cors = cors.max_age(cors_config.max_age as usize);

            if cors_config.allow_credentials {
                cors = cors.supports_credentials();
            }
        }

        App::new()
            .app_data(state)
            // Add CORS middleware with secure configuration
            .wrap(cors)
            // Add logging middleware
            .wrap(Logger::default())
            // Add default headers
            .wrap(DefaultHeaders::new().add(("Server", "LiteLLM-RS")))
            // Health check route
            .route("/health", web::get().to(health_check))
            // Configure AI API routes using the proper implementation
            .configure(routes::ai::configure_routes)
    }

    /// Start the HTTP server
    pub async fn start(self) -> Result<()> {
        let bind_addr = format!("{}:{}", self.config.host, self.config.port);

        info!("Starting HTTP server on {}", bind_addr);

        let state = web::Data::new(self.state);

        // Create and start the Actix-web server
        let server = ActixHttpServer::new(move || Self::create_app(state.clone()))
            .bind(&bind_addr)
            .map_err(|e| GatewayError::server(format!("Failed to bind to {}: {}", bind_addr, e)))?
            .run();

        info!("HTTP server listening on {}", bind_addr);

        // Start the server
        server
            .await
            .map_err(|e| GatewayError::server(format!("Server error: {}", e)))?;

        info!("HTTP server stopped");
        Ok(())
    }

    /// Graceful shutdown signal handler
    async fn shutdown_signal() {
        let ctrl_c = async {
            tokio::signal::ctrl_c()
                .await
                .expect("Failed to install Ctrl+C handler");
        };

        #[cfg(unix)]
        let terminate = async {
            tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
                .expect("Failed to install signal handler")
                .recv()
                .await;
        };

        #[cfg(not(unix))]
        let terminate = std::future::pending::<()>();

        tokio::select! {
            _ = ctrl_c => {
                info!("Received Ctrl+C signal, shutting down gracefully");
            },
            _ = terminate => {
                info!("Received terminate signal, shutting down gracefully");
            },
        }
    }

    /// Get server configuration
    pub fn config(&self) -> &ServerConfig {
        &self.config
    }

    /// Get application state
    pub fn state(&self) -> &AppState {
        &self.state
    }
}

impl AppState {
    /// Get gateway configuration
    #[allow(dead_code)] // May be used by handlers
    pub fn config(&self) -> &Config {
        &self.config
    }
}

/// Server builder for easier configuration
#[allow(dead_code)]
pub struct ServerBuilder {
    config: Option<Config>,
}

#[allow(dead_code)]
impl ServerBuilder {
    /// Create a new server builder
    pub fn new() -> Self {
        Self { config: None }
    }

    /// Set configuration
    pub fn with_config(mut self, config: Config) -> Self {
        self.config = Some(config);
        self
    }

    /// Build the HTTP server
    pub async fn build(self) -> Result<HttpServer> {
        let config = self
            .config
            .ok_or_else(|| GatewayError::Config("Configuration is required".to_string()))?;

        HttpServer::new(&config).await
    }
}

/// Run the server with automatic configuration loading
#[allow(dead_code)]
pub async fn run_server() -> Result<()> {
    info!("🚀 启动 Rust LiteLLM Gateway");

