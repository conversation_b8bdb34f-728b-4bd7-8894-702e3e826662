//! LiteLLM-RS - 高性能异步 AI 网关
//!
//! 支持多个 AI 提供商的异步网关服务

mod auth;
mod config;
mod core;
mod monitoring;
mod server;
mod storage;
mod utils;

use tracing::Level;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 初始化日志系统
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_target(false)
        .with_thread_ids(false)
        .init();

    // 启动服务器 (自动加载 config/gateway.yaml)
    server::run_server().await.map_err(|e| e.into())
}
