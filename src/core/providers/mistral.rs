//! Mistral AI provider implementation
//!
//! This module provides Mistral AI API integration.

use super::{BaseProvider, ModelPricing, Provider, ProviderError, ProviderType};
use crate::config::ProviderConfig;
use crate::core::models::{RequestContext, openai::*};
use crate::utils::error::Result;
use async_trait::async_trait;
use serde_json::json;
use std::collections::HashMap;
use tracing::{debug, info};

/// Mistral AI provider implementation
#[derive(Debug, Clone)]
pub struct MistralProvider {
    /// Base provider functionality
    base: BaseProvider,
    /// Model pricing cache
    pricing_cache: HashMap<String, ModelPricing>,
}

impl MistralProvider {
    /// Create a new Mistral AI provider
    pub async fn new(config: &ProviderConfig) -> Result<Self> {
        let base = BaseProvider::new(config)?;

        let base_url = config
            .base_url
            .clone()
            .unwrap_or_else(|| "https://api.mistral.ai".to_string());

        let provider = Self {
            base: BaseProvider { base_url, ..base },
            pricing_cache: Self::initialize_pricing_cache(),
        };

        info!(
            "Mistral AI provider '{}' initialized successfully",
            config.name
        );
        Ok(provider)
    }

    /// Initialize pricing cache with Mistral AI model prices
    fn initialize_pricing_cache() -> HashMap<String, ModelPricing> {
        let mut cache = HashMap::new();

        // Mistral models
        cache.insert(
            "mistral-tiny".to_string(),
            ModelPricing {
                model: "mistral-tiny".to_string(),
                input_cost_per_1k: 0.00014,
                output_cost_per_1k: 0.00042,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "mistral-small".to_string(),
            ModelPricing {
                model: "mistral-small".to_string(),
                input_cost_per_1k: 0.0006,
                output_cost_per_1k: 0.0018,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "mistral-medium".to_string(),
            ModelPricing {
                model: "mistral-medium".to_string(),
                input_cost_per_1k: 0.00275,
                output_cost_per_1k: 0.0081,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "mistral-large-latest".to_string(),
            ModelPricing {
                model: "mistral-large-latest".to_string(),
                input_cost_per_1k: 0.004,
                output_cost_per_1k: 0.012,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "open-mistral-7b".to_string(),
            ModelPricing {
                model: "open-mistral-7b".to_string(),
                input_cost_per_1k: 0.00025,
                output_cost_per_1k: 0.00025,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "open-mixtral-8x7b".to_string(),
            ModelPricing {
                model: "open-mixtral-8x7b".to_string(),
                input_cost_per_1k: 0.0007,
                output_cost_per_1k: 0.0007,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "open-mixtral-8x22b".to_string(),
            ModelPricing {
                model: "open-mixtral-8x22b".to_string(),
                input_cost_per_1k: 0.002,
                output_cost_per_1k: 0.006,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        // Embedding models
        cache.insert(
            "mistral-embed".to_string(),
            ModelPricing {
                model: "mistral-embed".to_string(),
                input_cost_per_1k: 0.0001,
                output_cost_per_1k: 0.0,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache
    }

    /// Convert OpenAI messages to Mistral format
    fn convert_messages_to_mistral(&self, messages: &[ChatMessage]) -> Vec<serde_json::Value> {
        messages
            .iter()
            .map(|message| {
                let role = match message.role {
                    MessageRole::System => "system",
                    MessageRole::User => "user",
                    MessageRole::Assistant => "assistant",
                    MessageRole::Tool => "tool",
                    MessageRole::Function => "function",
                };

                let content = match &message.content {
                    Some(MessageContent::Text(text)) => text.clone(),
                    Some(MessageContent::Parts(parts)) => {
                        // Mistral doesn't support multimodal content in the same way
                        // Extract text parts only
                        parts
                            .iter()
                            .filter_map(|part| match part {
                                ContentPart::Text { text } => Some(text.clone()),
                                _ => None,
                            })
                            .collect::<Vec<String>>()
                            .join(" ")
                    }
                    None => String::new(),
                };

                let mut msg = json!({
                    "role": role,
                    "content": content
                });

                // Add tool calls if present
                if let Some(tool_calls) = &message.tool_calls {
                    msg["tool_calls"] = json!(
                        tool_calls
                            .iter()
                            .map(|tc| json!({
                                "id": tc.id,
                                "type": tc.tool_type,
                                "function": {
                                    "name": tc.function.name,
                                    "arguments": tc.function.arguments
                                }
                            }))
                            .collect::<Vec<_>>()
                    );
                }

                msg
            })
            .collect()
    }

    /// Convert Mistral response to OpenAI format
    fn convert_mistral_response_to_openai(
        &self,
        mistral_response: serde_json::Value,
        model: &str,
    ) -> Result<ChatCompletionResponse> {
        let choices = mistral_response
            .get("choices")
            .and_then(|c| c.as_array())
            .ok_or_else(|| ProviderError::Parsing("No choices in response".to_string()))?;

        let openai_choices: Result<Vec<ChatCompletionChoice>> = choices
            .iter()
            .enumerate()
            .map(|(index, choice)| {
                let message = choice
                    .get("message")
                    .ok_or_else(|| ProviderError::Parsing("No message in choice".to_string()))?;

                let role = message
                    .get("role")
                    .and_then(|r| r.as_str())
                    .map(|r| match r {
                        "assistant" => MessageRole::Assistant,
                        "user" => MessageRole::User,
                        "system" => MessageRole::System,
                        "tool" => MessageRole::Tool,
                        _ => MessageRole::Assistant,
                    })
                    .unwrap_or(MessageRole::Assistant);

                let content = message
                    .get("content")
                    .and_then(|c| c.as_str())
                    .unwrap_or("")
                    .to_string();

                let tool_calls =
                    message
                        .get("tool_calls")
                        .and_then(|tc| tc.as_array())
                        .map(|calls| {
                            calls
                                .iter()
                                .filter_map(|call| {
                                    Some(ToolCall {
                                        id: call.get("id")?.as_str()?.to_string(),
                                        tool_type: call.get("type")?.as_str()?.to_string(),
                                        function: FunctionCall {
                                            name: call
                                                .get("function")?
                                                .get("name")?
                                                .as_str()?
                                                .to_string(),
                                            arguments: call
                                                .get("function")?
                                                .get("arguments")?
                                                .as_str()?
                                                .to_string(),
                                        },
                                    })
                                })
                                .collect()
                        });

                let finish_reason = choice
                    .get("finish_reason")
                    .and_then(|fr| fr.as_str())
                    .map(|fr| fr.to_string());

                Ok(ChatCompletionChoice {
                    index: index as u32,
                    message: ChatMessage {
                        role,
                        content: Some(MessageContent::Text(content)),
                        name: None,
                        function_call: None,
                        tool_calls,
                        tool_call_id: None,
                        audio: None,
                    },
                    finish_reason,
                    logprobs: None,
                })
            })
            .collect();

        let usage = mistral_response.get("usage").map(|u| Usage {
            prompt_tokens: u.get("prompt_tokens").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
            completion_tokens: u
                .get("completion_tokens")
                .and_then(|v| v.as_u64())
                .unwrap_or(0) as u32,
            total_tokens: u.get("total_tokens").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
            prompt_tokens_details: None,
            completion_tokens_details: None,
        });

        Ok(ChatCompletionResponse {
            id: mistral_response
                .get("id")
                .and_then(|id| id.as_str())
                .unwrap_or(&format!("chatcmpl-mistral-{}", uuid::Uuid::new_v4()))
                .to_string(),
            object: "chat.completion".to_string(),
            created: mistral_response
                .get("created")
                .and_then(|c| c.as_u64())
                .unwrap_or_else(|| chrono::Utc::now().timestamp() as u64),
            model: model.to_string(),
            choices: openai_choices?
                .into_iter()
                .map(|choice| ChatChoice {
                    index: choice.index,
                    message: choice.message,
                    logprobs: choice.logprobs.map(|_| Logprobs { content: None }),
                    finish_reason: choice.finish_reason,
                })
                .collect(),
            usage,
            system_fingerprint: None,
        })
    }
}

#[async_trait]
impl Provider for MistralProvider {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn provider_type(&self) -> ProviderType {
        ProviderType::Custom("mistral".to_string())
    }

    async fn supports_model(&self, model: &str) -> bool {
        self.base.is_model_supported(model)
            || model.starts_with("mistral")
            || model.starts_with("open-mistral")
            || model.starts_with("open-mixtral")
    }

    async fn supports_images(&self) -> bool {
        false // Mistral doesn't support images yet
    }

    async fn supports_embeddings(&self) -> bool {
        true // Mistral has embedding models
    }

    async fn supports_streaming(&self) -> bool {
        true // Mistral supports streaming
    }

    async fn list_models(&self) -> Result<Vec<Model>> {
        let url = format!("{}/v1/models", self.base.base_url);

        let response = self
            .base
            .client
            .get(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            // Fallback to known models if API call fails
            let known_models = vec![
                "mistral-tiny",
                "mistral-small",
                "mistral-medium",
                "mistral-large-latest",
                "open-mistral-7b",
                "open-mixtral-8x7b",
                "open-mixtral-8x22b",
                "mistral-embed",
            ];

            let models = known_models
                .into_iter()
                .map(|model| Model {
                    id: model.to_string(),
                    object: "model".to_string(),
                    created: chrono::Utc::now().timestamp() as u64,
                    owned_by: "mistralai".to_string(),
                })
                .collect();

            return Ok(models);
        }

        let models_response: serde_json::Value = self.base.parse_json_response(response).await?;

        let models = models_response
            .get("data")
            .and_then(|d| d.as_array())
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|model| {
                Some(Model {
                    id: model.get("id")?.as_str()?.to_string(),
                    object: model.get("object")?.as_str()?.to_string(),
                    created: model.get("created")?.as_u64()?,
                    owned_by: model.get("owned_by")?.as_str()?.to_string(),
                })
            })
            .collect();

        Ok(models)
    }

    async fn health_check(&self) -> Result<()> {
        debug!("Performing Mistral AI health check");

        let url = format!("{}/v1/models", self.base.base_url);

        let response = self
            .base
            .client
            .get(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(
                ProviderError::Unknown(format!("Health check failed: {}", response.status()))
                    .into(),
            )
        }
    }

    async fn chat_completion(
        &self,
        request: ChatCompletionRequest,
        _context: RequestContext,
    ) -> Result<ChatCompletionResponse> {
        debug!("Mistral AI chat completion for model: {}", request.model);

        let messages = self.convert_messages_to_mistral(&request.messages);

        let mut body = json!({
            "model": request.model,
            "messages": messages
        });

        // Add optional parameters
        if let Some(max_tokens) = request.max_tokens {
            body["max_tokens"] = json!(max_tokens);
        }
        if let Some(temperature) = request.temperature {
            body["temperature"] = json!(temperature);
        }
        if let Some(top_p) = request.top_p {
            body["top_p"] = json!(top_p);
        }
        if let Some(stream) = request.stream {
            body["stream"] = json!(stream);
        }
        if let Some(tools) = request.tools {
            body["tools"] = json!(tools);
        }
        if let Some(tool_choice) = request.tool_choice {
            body["tool_choice"] = json!(tool_choice);
        }

        let url = format!("{}/v1/chat/completions", self.base.base_url);

        let response = self
            .base
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();

            return Err(match status.as_u16() {
                401 => ProviderError::Authentication(error_text),
                429 => ProviderError::RateLimit(error_text),
                404 => ProviderError::ModelNotFound(error_text),
                400 => ProviderError::InvalidRequest(error_text),
                _ => ProviderError::Unknown(format!("HTTP {}: {}", status, error_text)),
            }
            .into());
        }

        let mistral_response: serde_json::Value = self.base.parse_json_response(response).await?;
        self.convert_mistral_response_to_openai(mistral_response, &request.model)
    }

    async fn completion(
        &self,
        request: CompletionRequest,
        _context: RequestContext,
    ) -> Result<CompletionResponse> {
        // Mistral doesn't have a separate completion endpoint
        // Convert to chat completion format
        let chat_request = ChatCompletionRequest {
            model: request.model.clone(),
            messages: vec![ChatMessage {
                role: MessageRole::User,
                content: Some(MessageContent::Text(request.prompt)),
                name: None,
                function_call: None,
                tool_calls: None,
                tool_call_id: None,
                audio: None,
            }],
            max_tokens: request.max_tokens,
            max_completion_tokens: None,
            temperature: request.temperature.map(|t| t as f32),
            top_p: request.top_p.map(|t| t as f32),
            n: request.n,
            stream: request.stream,
            stream_options: None,
            stop: request.stop,
            presence_penalty: request.presence_penalty.map(|p| p as f32),
            frequency_penalty: request.frequency_penalty.map(|f| f as f32),
            logit_bias: request
                .logit_bias
                .map(|bias| bias.into_iter().map(|(k, v)| (k, v as f32)).collect()),
            user: request.user,
            functions: None,
            function_call: None,
            tools: None,
            tool_choice: None,
            response_format: None,
            seed: None,
            logprobs: None,
            top_logprobs: None,
            modalities: None,
            audio: None,
        };

        let chat_response = self.chat_completion(chat_request, _context).await?;

        // Convert back to completion format
        let text = match &chat_response.choices.first().unwrap().message.content {
            Some(MessageContent::Text(text)) => text.clone(),
            Some(MessageContent::Parts(parts)) => parts
                .iter()
                .filter_map(|part| match part {
                    ContentPart::Text { text } => Some(text.clone()),
                    _ => None,
                })
                .collect::<Vec<String>>()
                .join(" "),
            None => String::new(),
        };

        Ok(CompletionResponse {
            id: chat_response.id.replace("chatcmpl", "cmpl"),
            object: "text_completion".to_string(),
            created: chat_response.created,
            model: request.model,
            choices: vec![CompletionChoice {
                text,
                index: 0,
                logprobs: None,
                finish_reason: chat_response.choices.first().unwrap().finish_reason.clone(),
            }],
            usage: chat_response.usage,
        })
    }

    async fn embedding(
        &self,
        request: EmbeddingRequest,
        _context: RequestContext,
    ) -> Result<EmbeddingResponse> {
        debug!("Mistral AI embedding for model: {}", request.model);

        let body = json!({
            "model": request.model,
            "input": request.input
        });

        let url = format!("{}/v1/embeddings", self.base.base_url);

        let response = self
            .base
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();

            return Err(match status.as_u16() {
                401 => ProviderError::Authentication(error_text),
                429 => ProviderError::RateLimit(error_text),
                404 => ProviderError::ModelNotFound(error_text),
                400 => ProviderError::InvalidRequest(error_text),
                _ => ProviderError::Unknown(format!("HTTP {}: {}", status, error_text)),
            }
            .into());
        }

        let embedding_response: serde_json::Value = self.base.parse_json_response(response).await?;

        let embeddings = embedding_response
            .get("data")
            .and_then(|d| d.as_array())
            .unwrap_or(&vec![])
            .iter()
            .enumerate()
            .filter_map(|(index, item)| {
                Some(EmbeddingObject {
                    object: "embedding".to_string(),
                    embedding: item
                        .get("embedding")?
                        .as_array()?
                        .iter()
                        .filter_map(|v| v.as_f64())
                        .collect(),
                    index: index as u32,
                })
            })
            .collect();

        let usage = embedding_response
            .get("usage")
            .map(|u| EmbeddingUsage {
                prompt_tokens: u.get("prompt_tokens").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
                total_tokens: u.get("total_tokens").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
            })
            .unwrap_or_default();

        Ok(EmbeddingResponse {
            object: "list".to_string(),
            data: embeddings,
            model: request.model,
            usage,
        })
    }

    async fn image_generation(
        &self,
        _request: ImageGenerationRequest,
        _context: RequestContext,
    ) -> Result<ImageGenerationResponse> {
        Err(ProviderError::InvalidRequest(
            "Image generation not supported by Mistral AI".to_string(),
        )
        .into())
    }

    async fn get_model_pricing(&self, model: &str) -> Result<ModelPricing> {
        if let Some(pricing) = self.pricing_cache.get(model) {
            Ok(pricing.clone())
        } else {
            // Return default pricing for unknown models
            Ok(ModelPricing {
                model: model.to_string(),
                input_cost_per_1k: 0.001,
                output_cost_per_1k: 0.003,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            })
        }
    }

    async fn calculate_cost(
        &self,
        model: &str,
        input_tokens: u32,
        output_tokens: u32,
    ) -> Result<f64> {
        let pricing = self.get_model_pricing(model).await?;

        let input_cost = (input_tokens as f64 / 1000.0) * pricing.input_cost_per_1k;
        let output_cost = (output_tokens as f64 / 1000.0) * pricing.output_cost_per_1k;

        Ok(input_cost + output_cost)
    }
}
