//! Aleph Alpha provider implementation
//!
//! This module provides Aleph Alpha API integration for Luminous models.

use super::{BaseProvider, ModelPricing, Provider, ProviderError, ProviderType};
use crate::config::ProviderConfig;
use crate::core::models::{RequestContext, openai::*};
use crate::utils::error::Result;
use async_trait::async_trait;
use serde_json::json;
use std::collections::HashMap;
use tracing::{debug, info};

/// Aleph Alpha provider implementation
#[derive(Debug, Clone)]
pub struct AlephAlphaProvider {
    /// Base provider functionality
    base: BaseProvider,
    /// Model pricing cache
    pricing_cache: HashMap<String, ModelPricing>,
}

impl AlephAlphaProvider {
    /// Create a new Aleph Alpha provider
    pub async fn new(config: &ProviderConfig) -> Result<Self> {
        let base = BaseProvider::new(config)?;

        let base_url = config
            .base_url
            .clone()
            .unwrap_or_else(|| "https://api.aleph-alpha.com".to_string());

        let provider = Self {
            base: BaseProvider { base_url, ..base },
            pricing_cache: Self::initialize_pricing_cache(),
        };

        info!(
            "Aleph Alpha provider '{}' initialized successfully",
            config.name
        );
        Ok(provider)
    }

    /// Initialize pricing cache with Aleph Alpha model prices
    fn initialize_pricing_cache() -> HashMap<String, ModelPricing> {
        let mut cache = HashMap::new();

        // Luminous models
        cache.insert(
            "luminous-base".to_string(),
            ModelPricing {
                model: "luminous-base".to_string(),
                input_cost_per_1k: 0.003,
                output_cost_per_1k: 0.0033,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "luminous-extended".to_string(),
            ModelPricing {
                model: "luminous-extended".to_string(),
                input_cost_per_1k: 0.0045,
                output_cost_per_1k: 0.0049,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "luminous-supreme".to_string(),
            ModelPricing {
                model: "luminous-supreme".to_string(),
                input_cost_per_1k: 0.0175,
                output_cost_per_1k: 0.0193,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache.insert(
            "luminous-supreme-control".to_string(),
            ModelPricing {
                model: "luminous-supreme-control".to_string(),
                input_cost_per_1k: 0.0375,
                output_cost_per_1k: 0.04125,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            },
        );

        cache
    }

    /// Convert OpenAI messages to Aleph Alpha format
    fn convert_messages_to_aleph_alpha(&self, messages: &[ChatMessage]) -> String {
        // Aleph Alpha expects a single prompt string
        messages
            .iter()
            .map(|msg| {
                let role = match msg.role {
                    MessageRole::System => "System",
                    MessageRole::User => "User",
                    MessageRole::Assistant => "Assistant",
                    MessageRole::Tool => "Tool",
                    MessageRole::Function => "Function",
                };

                let content = match &msg.content {
                    Some(MessageContent::Text(text)) => text.clone(),
                    Some(MessageContent::Parts(parts)) => parts
                        .iter()
                        .filter_map(|part| match part {
                            ContentPart::Text { text } => Some(text.clone()),
                            _ => None,
                        })
                        .collect::<Vec<String>>()
                        .join(" "),
                    None => String::new(),
                };

                format!("{}: {}", role, content)
            })
            .collect::<Vec<String>>()
            .join("\n\n")
    }

    /// Convert Aleph Alpha response to OpenAI format
    fn convert_aleph_alpha_response_to_openai(
        &self,
        aleph_response: serde_json::Value,
        model: &str,
    ) -> Result<ChatCompletionResponse> {
        let completions = aleph_response
            .get("completions")
            .and_then(|c| c.as_array())
            .ok_or_else(|| ProviderError::Parsing("No completions in response".to_string()))?;

        let choices: Vec<ChatCompletionChoice> = completions
            .iter()
            .enumerate()
            .map(|(index: index as u32, completion)| {
                let text = completion
                    .get("completion")
                    .and_then(|t| t.as_str())
                    .unwrap_or("")
                    .to_string();

                let finish_reason = completion
                    .get("finish_reason")
                    .and_then(|fr| fr.as_str())
                    .map(|r| match r {
                        "maximum_tokens" => "length",
                        "end_of_text" => "stop",
                        _ => "stop",
                    })
                    .unwrap_or("stop")
                    .to_string();

                ChatCompletionChoice {
                    index: index as u32,
                    message: ChatMessage {
                        role: MessageRole::Assistant,
                        content: Some(MessageContent::Text(text)),
                        name: None,
                        function_call: None,
                        tool_calls: None,
                        tool_call_id: None,
                        audio: None,
                    },
                    finish_reason: Some(finish_reason),
                    logprobs: None,
                }
            })
            .collect();

        // Extract usage information
        let usage = aleph_response.get("usage").map(|u| Usage {
            prompt_tokens: u.get("prompt_tokens").and_then(|v| v.as_u64()).unwrap_or(0),
            completion_tokens: u
                .get("completion_tokens")
                .and_then(|v| v.as_u64())
                .unwrap_or(0),
            total_tokens: u.get("total_tokens").and_then(|v| v.as_u64()).unwrap_or(0),
            prompt_tokens_details: None,
            completion_tokens_details: None,
        });

        Ok(ChatCompletionResponse {
            id: format!("chatcmpl-aleph-{}", uuid::Uuid::new_v4()),
            object: "chat.completion".to_string(),
            created: chrono::Utc::now().timestamp() as u64,
            model: model.to_string(),
            choices: choices
                .into_iter()
                .map(|choice| ChatChoice {
                    index: choice.index: index as u32,
                    message: choice.message,
                    logprobs: choice.logprobs.map(|_| Logprobs { content: None }),
                    finish_reason: choice.finish_reason,
                })
                .collect(),
            usage,
            system_fingerprint: None,
        })
    }
}

#[async_trait]
impl Provider for AlephAlphaProvider {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn provider_type(&self) -> ProviderType {
        ProviderType::Custom("aleph_alpha".to_string())
    }

    async fn supports_model(&self, model: &str) -> bool {
        self.base.is_model_supported(model) || model.starts_with("luminous")
    }

    async fn supports_images(&self) -> bool {
        true // Luminous models support multimodal input
    }

    async fn supports_embeddings(&self) -> bool {
        true // Aleph Alpha has embedding models
    }

    async fn supports_streaming(&self) -> bool {
        true // Aleph Alpha supports streaming
    }

    async fn list_models(&self) -> Result<Vec<Model>> {
        let known_models = vec![
            "luminous-base",
            "luminous-extended",
            "luminous-supreme",
            "luminous-supreme-control",
        ];

        let models = known_models
            .into_iter()
            .map(|model| Model {
                id: model.to_string(),
                object: "model".to_string(),
                created: chrono::Utc::now().timestamp() as u64,
                owned_by: "aleph-alpha".to_string(),
            })
            .collect();

        Ok(models)
    }

    async fn health_check(&self) -> Result<()> {
        debug!("Performing Aleph Alpha health check");
        Ok(()) // Aleph Alpha doesn't have a specific health endpoint
    }

    async fn chat_completion(
        &self,
        request: ChatCompletionRequest,
        _context: RequestContext,
    ) -> Result<ChatCompletionResponse> {
        debug!("Aleph Alpha chat completion for model: {}", request.model);

        let prompt = self.convert_messages_to_aleph_alpha(&request.messages);

        let mut body = json!({
            "model": request.model,
            "prompt": prompt
        });

        // Add optional parameters
        if let Some(max_tokens) = request.max_tokens {
            body["maximum_tokens"] = json!(max_tokens);
        }
        if let Some(temperature) = request.temperature {
            body["temperature"] = json!(temperature);
        }
        if let Some(top_p) = request.top_p {
            body["top_p"] = json!(top_p);
        }

        let url = format!("{}/complete", self.base.base_url);

        let response = self
            .base
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();

            return Err(match status.as_u16() {
                401 => ProviderError::Authentication(error_text),
                429 => ProviderError::RateLimit(error_text),
                404 => ProviderError::ModelNotFound(error_text),
                400 => ProviderError::InvalidRequest(error_text),
                _ => ProviderError::Unknown(format!("HTTP {}: {}", status, error_text)),
            }
            .into());
        }

        let aleph_response: serde_json::Value = self.base.parse_json_response(response).await?;
        self.convert_aleph_alpha_response_to_openai(aleph_response, &request.model)
    }

    async fn completion(
        &self,
        request: CompletionRequest,
        _context: RequestContext,
    ) -> Result<CompletionResponse> {
        debug!("Aleph Alpha completion for model: {}", request.model);

        let mut body = json!({
            "model": request.model,
            "prompt": request.prompt
        });

        if let Some(max_tokens) = request.max_tokens {
            body["maximum_tokens"] = json!(max_tokens);
        }
        if let Some(temperature) = request.temperature {
            body["temperature"] = json!(temperature);
        }
        if let Some(top_p) = request.top_p {
            body["top_p"] = json!(top_p);
        }

        let url = format!("{}/complete", self.base.base_url);

        let response = self
            .base
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();

            return Err(match status.as_u16() {
                401 => ProviderError::Authentication(error_text),
                429 => ProviderError::RateLimit(error_text),
                404 => ProviderError::ModelNotFound(error_text),
                400 => ProviderError::InvalidRequest(error_text),
                _ => ProviderError::Unknown(format!("HTTP {}: {}", status, error_text)),
            }
            .into());
        }

        let aleph_response: serde_json::Value = self.base.parse_json_response(response).await?;

        let empty_vec = vec![];
        let completions = aleph_response
            .get("completions")
            .and_then(|c| c.as_array())
            .unwrap_or(&empty_vec);

        let choices: Vec<CompletionChoice> = completions
            .iter()
            .enumerate()
            .map(|(index: index as u32, completion)| {
                let text = completion
                    .get("completion")
                    .and_then(|t| t.as_str())
                    .unwrap_or("")
                    .to_string();

                let finish_reason = completion
                    .get("finish_reason")
                    .and_then(|fr| fr.as_str())
                    .map(|r| match r {
                        "maximum_tokens" => "length",
                        "end_of_text" => "stop",
                        _ => "stop",
                    })
                    .unwrap_or("stop")
                    .to_string();

                CompletionChoice {
                    text,
                    index: index as u32,
                    logprobs: None,
                    finish_reason: Some(finish_reason),
                }
            })
            .collect();

        let usage = aleph_response.get("usage").map(|u| Usage {
            prompt_tokens: u.get("prompt_tokens").and_then(|v| v.as_u64()).unwrap_or(0),
            completion_tokens: u
                .get("completion_tokens")
                .and_then(|v| v.as_u64())
                .unwrap_or(0),
            total_tokens: u.get("total_tokens").and_then(|v| v.as_u64()).unwrap_or(0),
            prompt_tokens_details: None,
            completion_tokens_details: None,
        });

        Ok(CompletionResponse {
            id: format!("cmpl-aleph-{}", uuid::Uuid::new_v4()),
            object: "text_completion".to_string(),
            created: chrono::Utc::now().timestamp() as u64,
            model: request.model,
            choices,
            usage,
        })
    }

    async fn embedding(
        &self,
        request: EmbeddingRequest,
        _context: RequestContext,
    ) -> Result<EmbeddingResponse> {
        debug!("Aleph Alpha embedding for model: {}", request.model);

        let body = json!({
            "model": request.model,
            "prompt": request.input,
            "representation": "symmetric"
        });

        let url = format!("{}/embed", self.base.base_url);

        let response = self
            .base
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.base.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();

            return Err(match status.as_u16() {
                401 => ProviderError::Authentication(error_text),
                429 => ProviderError::RateLimit(error_text),
                404 => ProviderError::ModelNotFound(error_text),
                400 => ProviderError::InvalidRequest(error_text),
                _ => ProviderError::Unknown(format!("HTTP {}: {}", status, error_text)),
            }
            .into());
        }

        let aleph_response: serde_json::Value = self.base.parse_json_response(response).await?;

        let embedding_vec = aleph_response
            .get("embedding")
            .and_then(|e| e.as_array())
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|v| v.as_f64())
            .collect();

        let embeddings = vec![EmbeddingObject {
            object: "embedding".to_string(),
            embedding: embedding_vec,
            index: 0,
        }];

        Ok(EmbeddingResponse {
            object: "list".to_string(),
            data: embeddings,
            model: request.model,
            usage: EmbeddingUsage {
                prompt_tokens: 0, // Aleph Alpha doesn't provide token counts for embeddings
                total_tokens: 0,
            },
        })
    }

    async fn image_generation(
        &self,
        _request: ImageGenerationRequest,
        _context: RequestContext,
    ) -> Result<ImageGenerationResponse> {
        Err(ProviderError::InvalidRequest(
            "Image generation not supported by Aleph Alpha".to_string(),
        )
        .into())
    }

    async fn get_model_pricing(&self, model: &str) -> Result<ModelPricing> {
        if let Some(pricing) = self.pricing_cache.get(model) {
            Ok(pricing.clone())
        } else {
            Ok(ModelPricing {
                model: model.to_string(),
                input_cost_per_1k: 0.005,
                output_cost_per_1k: 0.005,
                currency: "USD".to_string(),
                updated_at: chrono::Utc::now(),
            })
        }
    }

    async fn calculate_cost(
        &self,
        model: &str,
        input_tokens: u32,
        output_tokens: u32,
    ) -> Result<f64> {
        let pricing = self.get_model_pricing(model).await?;

        let input_cost = (input_tokens as f64 / 1000.0) * pricing.input_cost_per_1k;
        let output_cost = (output_tokens as f64 / 1000.0) * pricing.output_cost_per_1k;

        Ok(input_cost + output_cost)
    }
}
