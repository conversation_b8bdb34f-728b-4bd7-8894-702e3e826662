//! # LiteLLM-RS
//!
//! A high-performance AI Gateway written in Rust, providing OpenAI-compatible APIs
//! with intelligent routing, load balancing, caching, and enterprise features.
//!
//! ## Features
//!
//! - **OpenAI Compatible**: Full compatibility with OpenAI API endpoints
//! - **Multi-Provider**: Support for 100+ AI providers (OpenAI, Anthropic, Azure, etc.)
//! - **Intelligent Routing**: Smart load balancing with multiple strategies
//! - **High Performance**: Built with Rust and Tokio for maximum throughput
//! - **Enterprise Ready**: Authentication, authorization, monitoring, and audit logs
//! - **Caching**: Multi-tier caching including semantic caching
//! - **Real-time**: WebSocket support for real-time AI interactions
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use litellm_rs::{Gateway, Config};
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = Config::from_file("config/gateway.yaml").await?;
//!     let gateway = Gateway::new(config).await?;
//!     gateway.run().await?;
//!     Ok(())
//! }
//! ```

#![warn(missing_docs)]
#![warn(clippy::all)]
#![allow(clippy::module_inception)]

// 公共模块导出
mod auth;
pub mod config;
pub mod core;
mod monitoring;
pub mod sdk; // 新增SDK模块
pub mod server;
pub mod storage;
pub mod utils;

// 重新导出主要类型
pub use config::Config;
pub use utils::error::{GatewayError, Result};

use tracing::info;

/// A minimal LiteLLM Gateway implementation
pub struct Gateway {
    config: Config,
    server: server::HttpServer,
}

impl Gateway {
    /// Create a new gateway instance
    pub async fn new(config: Config) -> Result<Self> {
        info!("Creating new gateway instance");

        // Create HTTP server
        let server = server::HttpServer::new(&config).await?;

        Ok(Self { config, server })
    }

    /// Run the gateway server
    pub async fn run(self) -> Result<()> {
        info!("Starting LiteLLM Gateway");
        info!("Configuration: {:#?}", self.config);

        // Start HTTP server
        self.server.start().await?;

        Ok(())
    }
}

// 版本信息
/// Current version of the crate
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
/// Name of the crate
pub const NAME: &str = env!("CARGO_PKG_NAME");
/// Description of the crate
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Gateway 构建信息
#[derive(Debug, Clone)]
pub struct BuildInfo {
    /// 版本号
    pub version: &'static str,
    /// 构建时间
    pub build_time: &'static str,
    /// Git 提交哈希
    pub git_hash: &'static str,
    /// Rust 版本
    pub rust_version: &'static str,
}

impl Default for BuildInfo {
    fn default() -> Self {
        Self {
            version: VERSION,
            build_time: "unknown",
            git_hash: "unknown",
            rust_version: "unknown",
        }
    }
}

/// 获取构建信息
pub fn build_info() -> BuildInfo {
    BuildInfo::default()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_build_info() {
        let info = build_info();
        assert!(!info.version.is_empty());
        assert_eq!(info.version, VERSION);
    }

    #[test]
    fn test_constants() {
        // Test that constants are defined and have expected values
        assert_eq!(VERSION, env!("CARGO_PKG_VERSION"));
        assert_eq!(NAME, env!("CARGO_PKG_NAME"));
        assert_eq!(DESCRIPTION, env!("CARGO_PKG_DESCRIPTION"));
    }
}
