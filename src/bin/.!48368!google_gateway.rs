//! 配置驱动的 LiteLLM Gateway
//!
//! 通过YAML配置文件管理所有设置，支持Google API代理

use actix_web::{
    App, HttpResponse, HttpServer, Result as ActixResult,
    middleware::{DefaultHead<PERSON>, Logger},
    web,
};

use actix_cors::Cors;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info, instrument};

