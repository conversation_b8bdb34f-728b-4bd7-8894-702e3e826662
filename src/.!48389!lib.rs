//! # LiteLLM-RS
//!
//! A high-performance AI Gateway written in Rust, providing OpenAI-compatible APIs
//! with intelligent routing, load balancing, caching, and enterprise features.
//!
//! ## Features
//!
//! - **OpenAI Compatible**: Full compatibility with OpenAI API endpoints
//! - **Multi-Provider**: Support for 100+ AI providers (OpenAI, Anthropic, Azure, etc.)
//! - **Intelligent Routing**: Smart load balancing with multiple strategies
//! - **High Performance**: Built with Rust and Tokio for maximum throughput
//! - **Enterprise Ready**: Authentication, authorization, monitoring, and audit logs
//! - **Caching**: Multi-tier caching including semantic caching
//! - **Real-time**: WebSocket support for real-time AI interactions
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use litellm_rs::{Gateway, Config};
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = Config::from_file("config/gateway.yaml").await?;
//!     let gateway = Gateway::new(config).await?;
//!     gateway.run().await?;
//!     Ok(())
//! }
//! ```

#![warn(missing_docs)]
#![warn(clippy::all)]
#![allow(clippy::module_inception)]

