# LiteLLM Gateway API 测试用例

这个文档包含了各种API端点的测试body示例，您可以使用curl、Postman或其他HTTP客户端进行测试。

## 🚀 基础设置

**Base URL**: `http://localhost:8080` (默认端口)
**Headers**: 
```
Content-Type: application/json
Authorization: Bearer your-api-key-here
```

## 1. Chat Completions API (OpenAI兼容)

### 基础聊天请求
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

### 多轮对话
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant that explains complex topics simply."
    },
    {
      "role": "user",
      "content": "What is quantum computing?"
    },
    {
      "role": "assistant",
      "content": "Quantum computing is a type of computation that uses quantum mechanics..."
    },
    {
      "role": "user",
      "content": "Can you give me a simple analogy?"
    }
  ],
  "temperature": 0.8,
  "max_tokens": 200,
  "top_p": 0.9
}
```

### 流式响应
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Write a short story about a robot learning to paint."
    }
  ],
  "stream": true,
  "temperature": 0.9,
  "max_tokens": 500
}
```

### 函数调用 (Function Calling)
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "What's the weather like in Tokyo?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "Get current weather for a city",
        "parameters": {
          "type": "object",
          "properties": {
            "city": {
              "type": "string",
              "description": "The city name"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"],
              "description": "Temperature unit"
            }
          },
          "required": ["city"]
        }
      }
    }
  ],
  "tool_choice": "auto"
}
```

## 2. Text Completions API

### 基础文本补全
```bash
curl -X POST http://localhost:8080/v1/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "text-davinci-003",
    "prompt": "The future of artificial intelligence is",
    "max_tokens": 100,
    "temperature": 0.7,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  }'
```

### 多个补全选项
```json
{
  "model": "text-davinci-003",
  "prompt": "Write a creative opening line for a science fiction novel:",
  "max_tokens": 50,
  "temperature": 1.0,
  "n": 3,
  "stop": ["\n", "."]
}
```

## 3. Embeddings API

### 文本嵌入
```bash
curl -X POST http://localhost:8080/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "text-embedding-ada-002",
    "input": "The quick brown fox jumps over the lazy dog"
  }'
```

### 批量嵌入
```json
{
  "model": "text-embedding-ada-002",
  "input": [
    "Hello world",
    "How are you today?",
    "This is a test sentence",
    "Machine learning is fascinating"
  ]
}
```

## 4. Models API

### 列出可用模型
```bash
curl -X GET http://localhost:8080/v1/models \
  -H "Authorization: Bearer your-api-key"
```

### 获取特定模型信息
```bash
curl -X GET http://localhost:8080/v1/models/gpt-3.5-turbo \
  -H "Authorization: Bearer your-api-key"
```

## 5. Health Check

### 基础健康检查
```bash
curl -X GET http://localhost:8080/health
```

### 详细健康检查
```bash
curl -X GET http://localhost:8080/health/detailed
```

## 6. 认证相关测试

### 用户注册
```bash
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### 用户登录
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

## 7. 错误测试用例

### 无效模型
```json
{
  "model": "invalid-model-name",
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ]
}
```

### 缺少必需字段
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ]
}
```

### 无效参数值
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ],
  "temperature": 5.0,
  "max_tokens": -100
}
```

## 8. 高级测试场景

### 大文本处理
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Please summarize this long text: [插入一个很长的文本，比如一篇文章或文档]"
    }
  ],
  "max_tokens": 500,
  "temperature": 0.3
}
```

### JSON模式响应
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Generate a JSON object with user information including name, age, and hobbies"
    }
  ],
  "response_format": {
    "type": "json_object"
  }
}
```

## 🧪 测试脚本示例

您也可以创建一个简单的测试脚本：

```bash
#!/bin/bash

BASE_URL="http://localhost:8080"
API_KEY="your-api-key-here"

# 测试健康检查
echo "Testing health check..."
curl -s "$BASE_URL/health" | jq .

# 测试聊天API
echo "Testing chat completions..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 50
  }' | jq .

echo "All tests completed!"
```

## 🧪 如何运行测试

### 1. 使用Bash脚本
```bash
# 给脚本执行权限
chmod +x scripts/test_api.sh

# 运行测试
./scripts/test_api.sh

# 或指定自定义URL和API Key
./scripts/test_api.sh http://localhost:8080 your-api-key
```

### 2. 使用Python脚本
```bash
# 安装依赖
pip install requests

# 运行测试
python scripts/test_api.py

# 或指定自定义参数
python scripts/test_api.py --base-url http://localhost:8080 --api-key your-api-key
```

### 3. 使用Rust集成测试
```bash
# 运行所有测试
cargo test

# 运行特定测试
cargo test test_chat_completions_basic

# 运行集成测试
cargo test --test integration_tests
```

### 4. 使用Postman
1. 导入 `tests/LiteLLM_Gateway.postman_collection.json`
2. 设置环境变量 `base_url` 和 `api_key`
3. 运行整个集合或单个请求

## 📝 注意事项

1. **API Key**: 确保使用有效的API密钥
2. **Rate Limiting**: 注意请求频率限制
3. **Model Availability**: 确认所请求的模型在您的配置中可用
4. **Error Handling**: 测试各种错误场景以确保robust性
5. **Performance**: 监控响应时间和资源使用情况
6. **服务器状态**: 确保LiteLLM Gateway服务正在运行
7. **网络连接**: 检查网络连接和防火墙设置
