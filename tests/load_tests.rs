//! Load testing for litellm-rs
//!
//! This module contains load tests to verify the system can handle
//! high concurrent loads and stress conditions.

use litellm_rs::core::cache_manager::{CacheConfig, CacheManager};
use litellm_rs::core::models::openai::*;
use litellm_rs::utils::string_pool::intern_string;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::test;

/// Test high concurrent cache operations
#[tokio::test]
async fn test_high_concurrency_cache() {
    let config = CacheConfig {
        max_entries: 10000,
        default_ttl: Duration::from_secs(3600),
        enable_semantic: false,
        similarity_threshold: 0.95,
        min_prompt_length: 10,
        enable_compression: false,
    };

    let cache = Arc::new(CacheManager::new(config).unwrap());
    let semaphore = Arc::new(Semaphore::new(1000)); // Limit concurrent operations

    let start = Instant::now();
    let mut handles = Vec::new();

    // Spawn 1000 concurrent tasks
    for i in 0..1000 {
        let cache = cache.clone();
        let semaphore = semaphore.clone();

        let handle = tokio::spawn(async move {
            let _permit = semaphore.acquire().await.unwrap();

            let key = litellm_rs::core::cache_manager::CacheKey {
                model: intern_string("gpt-4"),
                request_hash: i,
                user_id: None,
            };

            let response = ChatCompletionResponse {
                id: format!("load_test_{}", i),
                object: "chat.completion".to_string(),
                created: 1234567890,
                model: "gpt-4".to_string(),
                choices: vec![],
                usage: None,
                system_fingerprint: None,
            };

            // Perform multiple operations per task
            for _ in 0..10 {
                cache.put(key.clone(), response.clone()).await.unwrap();
                cache.get(&key).await.unwrap();
            }
        });

        handles.push(handle);
    }

    // Wait for all tasks to complete
    for handle in handles {
        handle.await.unwrap();
    }

    let duration = start.elapsed();
    let ops_per_second = (1000 * 10 * 2) as f64 / duration.as_secs_f64(); // 2 ops per iteration

    println!("Load test completed in {:?}", duration);
    println!("Operations per second: {:.2}", ops_per_second);

    // Should handle at least 1000 ops/sec
    assert!(
        ops_per_second > 1000.0,
        "Performance below threshold: {} ops/sec",
        ops_per_second
    );
}

/// Test memory pressure scenarios
#[tokio::test]
async fn test_memory_pressure() {
    let config = CacheConfig {
        max_entries: 1000, // Smaller cache to trigger evictions
        default_ttl: Duration::from_secs(3600),
        enable_semantic: false,
        similarity_threshold: 0.95,
        min_prompt_length: 10,
        enable_compression: false,
    };

    let cache = Arc::new(CacheManager::new(config).unwrap());

    // Fill cache beyond capacity
    for i in 0..2000 {
        let key = litellm_rs::core::cache_manager::CacheKey {
            model: intern_string("gpt-4"),
            request_hash: i,
            user_id: None,
        };

        let response = ChatCompletionResponse {
            id: format!("memory_test_{}", i),
            object: "chat.completion".to_string(),
            created: 1234567890,
            model: "gpt-4".to_string(),
            choices: vec![],
            usage: None,
            system_fingerprint: None,
        };

        cache.put(key, response).await.unwrap();
    }

    // Verify cache is still functional
    let test_key = litellm_rs::core::cache_manager::CacheKey {
        model: intern_string("gpt-4"),
        request_hash: 9999,
        user_id: None,
    };

    let test_response = ChatCompletionResponse {
        id: "final_test".to_string(),
        object: "chat.completion".to_string(),
        created: 1234567890,
        model: "gpt-4".to_string(),
        choices: vec![],
        usage: None,
        system_fingerprint: None,
    };

    cache.put(test_key.clone(), test_response).await.unwrap();
    let retrieved = cache.get(&test_key).await.unwrap();

    assert!(retrieved.is_some());
    println!("Memory pressure test passed - cache remained functional");
}

/// Test sustained load over time
#[tokio::test]
async fn test_sustained_load() {
    let config = CacheConfig::default();
    let cache = Arc::new(CacheManager::new(config).unwrap());

    let start = Instant::now();
    let test_duration = Duration::from_secs(10); // Run for 10 seconds
    let mut operation_count = 0;

    while start.elapsed() < test_duration {
        let mut handles = Vec::new();

        // Spawn batch of concurrent operations
        for i in 0..50 {
            let cache = cache.clone();
            let batch_id = operation_count / 50;

            let handle = tokio::spawn(async move {
                let key = litellm_rs::core::cache_manager::CacheKey {
                    model: intern_string("gpt-4"),
                    request_hash: (batch_id * 50 + i) as u64,
                    user_id: None,
                };

                let response = ChatCompletionResponse {
                    id: format!("sustained_test_{}_{}", batch_id, i),
                    object: "chat.completion".to_string(),
                    created: 1234567890,
                    model: "gpt-4".to_string(),
                    choices: vec![],
                    usage: None,
                    system_fingerprint: None,
                };

                cache.put(key.clone(), response).await.unwrap();
                cache.get(&key).await.unwrap()
            });

            handles.push(handle);
        }

        // Wait for batch to complete
        for handle in handles {
            handle.await.unwrap();
        }

        operation_count += 100; // 50 put + 50 get operations

        // Small delay to prevent overwhelming the system
        tokio::time::sleep(Duration::from_millis(10)).await;
    }

    let total_duration = start.elapsed();
    let ops_per_second = operation_count as f64 / total_duration.as_secs_f64();

    println!("Sustained load test completed");
    println!("Total operations: {}", operation_count);
    println!("Duration: {:?}", total_duration);
    println!("Average ops/sec: {:.2}", ops_per_second);

    // Should maintain reasonable performance over time
    assert!(
        ops_per_second > 500.0,
        "Sustained performance below threshold: {} ops/sec",
        ops_per_second
    );
}

/// Test error handling under load
#[tokio::test]
async fn test_error_handling_under_load() {
    let config = CacheConfig::default();
    let cache = Arc::new(CacheManager::new(config).unwrap());

    let mut handles = Vec::new();
    let mut success_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));
    let mut error_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));

    // Spawn tasks that might encounter errors
    for i in 0..500 {
        let cache = cache.clone();
        let success_count = success_count.clone();
        let error_count = error_count.clone();

        let handle = tokio::spawn(async move {
            let key = litellm_rs::core::cache_manager::CacheKey {
                model: intern_string("gpt-4"),
                request_hash: i,
                user_id: None,
            };

            // Try operations that might fail
            match cache.get(&key).await {
                Ok(_) => {
                    success_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
                Err(_) => {
                    error_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
            }

            let response = ChatCompletionResponse {
                id: format!("error_test_{}", i),
                object: "chat.completion".to_string(),
                created: 1234567890,
                model: "gpt-4".to_string(),
                choices: vec![],
                usage: None,
                system_fingerprint: None,
            };

            match cache.put(key, response).await {
                Ok(_) => {
                    success_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
                Err(_) => {
                    error_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
            }
        });

        handles.push(handle);
    }

    // Wait for all tasks
    for handle in handles {
        handle.await.unwrap();
    }

    let final_success = success_count.load(std::sync::atomic::Ordering::Relaxed);
    let final_errors = error_count.load(std::sync::atomic::Ordering::Relaxed);

    println!("Error handling test completed");
    println!("Successful operations: {}", final_success);
    println!("Failed operations: {}", final_errors);

    // Most operations should succeed
    let success_rate = final_success as f64 / (final_success + final_errors) as f64;
    assert!(
        success_rate > 0.95,
        "Success rate too low: {:.2}%",
        success_rate * 100.0
    );
}

/// Test resource cleanup under load
#[tokio::test]
async fn test_resource_cleanup() {
    let config = CacheConfig {
        max_entries: 100,
        default_ttl: Duration::from_millis(100), // Very short TTL
        enable_semantic: false,
        similarity_threshold: 0.95,
        min_prompt_length: 10,
        enable_compression: false,
    };

    let cache = Arc::new(CacheManager::new(config).unwrap());

    // Fill cache with short-lived entries
    for i in 0..200 {
        let key = litellm_rs::core::cache_manager::CacheKey {
            model: intern_string("gpt-4"),
            request_hash: i,
            user_id: None,
        };

        let response = ChatCompletionResponse {
            id: format!("cleanup_test_{}", i),
            object: "chat.completion".to_string(),
            created: 1234567890,
            model: "gpt-4".to_string(),
            choices: vec![],
            usage: None,
            system_fingerprint: None,
        };

        cache.put(key, response).await.unwrap();
    }

    // Wait for TTL to expire
    tokio::time::sleep(Duration::from_millis(200)).await;

    // Add new entries to trigger cleanup
    for i in 200..300 {
        let key = litellm_rs::core::cache_manager::CacheKey {
            model: intern_string("gpt-4"),
            request_hash: i,
            user_id: None,
        };

        let response = ChatCompletionResponse {
            id: format!("cleanup_test_new_{}", i),
            object: "chat.completion".to_string(),
            created: 1234567890,
            model: "gpt-4".to_string(),
            choices: vec![],
            usage: None,
            system_fingerprint: None,
        };

        cache.put(key, response).await.unwrap();
    }

    // Verify that old entries are cleaned up
    let stats = cache.stats();
    println!("Cache stats after cleanup: {:?}", stats);

    // Cache should have cleaned up expired entries
    // This is a basic test - in practice you'd want more sophisticated cleanup verification
    println!("Resource cleanup test completed");
}
