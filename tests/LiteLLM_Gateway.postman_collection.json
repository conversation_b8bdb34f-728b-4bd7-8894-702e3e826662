{"info": {"name": "LiteLLM Gateway API", "description": "Complete test collection for LiteLLM Gateway API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "api_key", "value": "your-api-key-here", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{api_key}}", "type": "string"}]}, "item": [{"name": "Health Checks", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}}}]}, {"name": "Models API", "item": [{"name": "List Models", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/models", "host": ["{{base_url}}"], "path": ["v1", "models"]}}}]}, {"name": "Chat Completions", "item": [{"name": "Basic Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gpt-3.5-turbo\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello, how are you?\"\n    }\n  ],\n  \"temperature\": 0.7,\n  \"max_tokens\": 150\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Multi-turn Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant that explains complex topics simply.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"What is quantum computing?\"\n    },\n    {\n      \"role\": \"assistant\",\n      \"content\": \"Quantum computing is a type of computation that uses quantum mechanics...\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Can you give me a simple analogy?\"\n    }\n  ],\n  \"temperature\": 0.8,\n  \"max_tokens\": 200\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Streaming Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gpt-3.5-turbo\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Write a short story about a robot learning to paint.\"\n    }\n  ],\n  \"stream\": true,\n  \"temperature\": 0.9,\n  \"max_tokens\": 500\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Function Calling", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"What's the weather like in Tokyo?\"\n    }\n  ],\n  \"tools\": [\n    {\n      \"type\": \"function\",\n      \"function\": {\n        \"name\": \"get_weather\",\n        \"description\": \"Get current weather for a city\",\n        \"parameters\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": {\n              \"type\": \"string\",\n              \"description\": \"The city name\"\n            },\n            \"unit\": {\n              \"type\": \"string\",\n              \"enum\": [\"celsius\", \"fahrenheit\"],\n              \"description\": \"Temperature unit\"\n            }\n          },\n          \"required\": [\"city\"]\n        }\n      }\n    }\n  ],\n  \"tool_choice\": \"auto\"\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}, {"name": "Text Completions", "item": [{"name": "Basic Completion", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-davinci-003\",\n  \"prompt\": \"The future of artificial intelligence is\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7,\n  \"top_p\": 1.0,\n  \"frequency_penalty\": 0.0,\n  \"presence_penalty\": 0.0\n}"}, "url": {"raw": "{{base_url}}/v1/completions", "host": ["{{base_url}}"], "path": ["v1", "completions"]}}}, {"name": "Multiple Completions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-davinci-003\",\n  \"prompt\": \"Write a creative opening line for a science fiction novel:\",\n  \"max_tokens\": 50,\n  \"temperature\": 1.0,\n  \"n\": 3,\n  \"stop\": [\"\\n\", \".\"]\n}"}, "url": {"raw": "{{base_url}}/v1/completions", "host": ["{{base_url}}"], "path": ["v1", "completions"]}}}]}, {"name": "Embeddings", "item": [{"name": "Single Embedding", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-embedding-ada-002\",\n  \"input\": \"The quick brown fox jumps over the lazy dog\"\n}"}, "url": {"raw": "{{base_url}}/v1/embeddings", "host": ["{{base_url}}"], "path": ["v1", "embeddings"]}}}, {"name": "<PERSON><PERSON> Embeddings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-embedding-ada-002\",\n  \"input\": [\n    \"Hello world\",\n    \"How are you today?\",\n    \"This is a test sentence\",\n    \"Machine learning is fascinating\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/embeddings", "host": ["{{base_url}}"], "path": ["v1", "embeddings"]}}}]}, {"name": "Error Tests", "item": [{"name": "Invalid Model", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"invalid-model-name\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Missing Required Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Invalid Parameters", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gpt-3.5-turbo\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello\"\n    }\n  ],\n  \"temperature\": 5.0,\n  \"max_tokens\": -100\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}]}