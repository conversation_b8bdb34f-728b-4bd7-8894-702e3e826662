{"info": {"name": "Google API Tests - LiteLLM Gateway", "description": "Test collection for Google AI services (Gemini, Vertex AI, PaLM) through LiteLLM Gateway", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "google_api_key", "value": "your-google-api-key", "type": "string"}, {"key": "vertex_token", "value": "your-vertex-ai-token", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{google_api_key}}", "type": "string"}]}, "item": [{"name": "Gemini API Tests", "item": [{"name": "Gemini 1.5 Pro - Basic Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! Can you explain quantum computing in simple terms?\"\n    }\n  ],\n  \"temperature\": 0.7,\n  \"max_tokens\": 500\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Gemini 1.5 Flash - Quick Response", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-flash\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"写一个Python快速排序的例子\"\n    }\n  ],\n  \"temperature\": 0.3,\n  \"max_tokens\": 300\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Gemini Pro Vision - Image Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-pro-vision\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": [\n        {\n          \"type\": \"text\",\n          \"text\": \"What do you see in this image? Please describe it in detail.\"\n        },\n        {\n          \"type\": \"image_url\",\n          \"image_url\": {\n            \"url\": \"https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg\"\n          }\n        }\n      ]\n    }\n  ],\n  \"max_tokens\": 300\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Gemini - Streaming Response", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"写一个关于未来城市的科幻短故事\"\n    }\n  ],\n  \"stream\": true,\n  \"temperature\": 0.9,\n  \"max_tokens\": 800\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Gemini - Multi-turn Conversation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"你是一个专业的AI助手，擅长解释复杂的技术概念。\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"什么是机器学习？\"\n    },\n    {\n      \"role\": \"assistant\",\n      \"content\": \"机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"能给我举个具体的例子吗？\"\n    }\n  ],\n  \"temperature\": 0.8,\n  \"max_tokens\": 400\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}, {"name": "Vertex AI Tests", "item": [{"name": "Vertex AI - Enterprise Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{vertex_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"分析这个商业计划的可行性：开设一家AI驱动的咖啡店，使用机器学习来预测客户偏好并自动调整菜单。\"\n    }\n  ],\n  \"temperature\": 0.5,\n  \"max_tokens\": 800,\n  \"top_p\": 0.8\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Vertex AI - Text Embeddings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{vertex_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-embedding-004\",\n  \"input\": [\n    \"人工智能正在改变世界\",\n    \"机器学习是AI的核心技术\",\n    \"深度学习推动了AI的发展\",\n    \"自然语言处理让机器理解人类语言\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/embeddings", "host": ["{{base_url}}"], "path": ["v1", "embeddings"]}}}]}, {"name": "PaLM API Tests (Legacy)", "item": [{"name": "PaLM - Text Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"text-bison-001\",\n  \"prompt\": \"写一篇关于可持续发展重要性的文章开头\",\n  \"temperature\": 0.7,\n  \"max_tokens\": 300,\n  \"top_p\": 0.8,\n  \"top_k\": 40\n}"}, "url": {"raw": "{{base_url}}/v1/completions", "host": ["{{base_url}}"], "path": ["v1", "completions"]}}}, {"name": "PaLM - Chat Mode", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"chat-bison-001\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"你好，我想了解一下气候变化对全球经济的影响\"\n    }\n  ],\n  \"temperature\": 0.6,\n  \"max_tokens\": 400\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}, {"name": "Advanced Features", "item": [{"name": "Safety Settings Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"请解释核能发电的工作原理和安全措施\"\n    }\n  ],\n  \"safety_settings\": [\n    {\n      \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n      \"threshold\": \"BLOCK_MEDIUM_AND_ABOVE\"\n    }\n  ],\n  \"max_tokens\": 500\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Function Calling Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"今天北京的天气怎么样？温度是多少？\"\n    }\n  ],\n  \"tools\": [\n    {\n      \"type\": \"function\",\n      \"function\": {\n        \"name\": \"get_weather\",\n        \"description\": \"获取指定城市的天气信息\",\n        \"parameters\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": {\n              \"type\": \"string\",\n              \"description\": \"城市名称\"\n            },\n            \"unit\": {\n              \"type\": \"string\",\n              \"enum\": [\"celsius\", \"fahrenheit\"],\n              \"description\": \"温度单位\"\n            }\n          },\n          \"required\": [\"city\"]\n        }\n      }\n    }\n  ],\n  \"tool_choice\": \"auto\"\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Long Context Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-pro\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"请总结以下长文档的要点：\\n\\n[这里可以放入一个很长的文档内容，Gemini 1.5 Pro支持最多200万token的上下文长度]\\n\\n请提供详细的总结和关键洞察。\"\n    }\n  ],\n  \"temperature\": 0.3,\n  \"max_tokens\": 1000\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}, {"name": "Erro<PERSON>", "item": [{"name": "Invalid Google Model", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"invalid-google-model\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Rate Limit Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"gemini-1.5-flash\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"This is a rate limit test\"\n    }\n  ],\n  \"max_tokens\": 10\n}"}, "url": {"raw": "{{base_url}}/v1/chat/completions", "host": ["{{base_url}}"], "path": ["v1", "chat", "completions"]}}}]}]}