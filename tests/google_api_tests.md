# Google API 测试用例

这个文档包含了使用Google AI服务的具体测试用例，包括Gemini、Vertex AI和PaLM API。

## 🔑 API密钥获取

### 1. Google AI Studio (Gemini API)
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 设置环境变量: `GOOGLE_API_KEY=your_api_key`

### 2. Vertex AI
1. 在 [Google Cloud Console](https://console.cloud.google.com/) 创建项目
2. 启用 Vertex AI API
3. 创建服务账户并下载JSON密钥文件
4. 设置环境变量: `GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json`

### 3. PaLM API (已弃用)
1. 访问 [Google AI](https://developers.generativeai.google/)
2. 获取API密钥

## 🧪 Gemini API 测试用例

### 基础文本生成
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "解释一下量子计算的基本原理"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 500
  }'
```

### 多轮对话
```json
{
  "model": "gemini-1.5-flash",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的AI助手，擅长解释复杂概念。"
    },
    {
      "role": "user",
      "content": "什么是机器学习？"
    },
    {
      "role": "assistant",
      "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。"
    },
    {
      "role": "user",
      "content": "能给我举个具体的例子吗？"
    }
  ],
  "temperature": 0.8,
  "max_tokens": 300
}
```

### 视觉理解 (Gemini Pro Vision)
```json
{
  "model": "gemini-pro-vision",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "这张图片里有什么？请详细描述。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
          }
        }
      ]
    }
  ],
  "max_tokens": 300
}
```

### 代码生成
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "用Python写一个快速排序算法，并添加详细注释"
    }
  ],
  "temperature": 0.3,
  "max_tokens": 800
}
```

### 流式响应
```json
{
  "model": "gemini-1.5-flash",
  "messages": [
    {
      "role": "user",
      "content": "写一个关于未来城市的短故事"
    }
  ],
  "stream": true,
  "temperature": 0.9,
  "max_tokens": 1000
}
```

## 🏢 Vertex AI 测试用例

### 企业级聊天
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-vertex-token" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "分析这个商业计划的可行性：开设一家AI驱动的咖啡店"
      }
    ],
    "temperature": 0.5,
    "max_tokens": 800,
    "top_p": 0.8
  }'
```

### 文本嵌入
```json
{
  "model": "text-embedding-004",
  "input": [
    "人工智能正在改变世界",
    "机器学习是AI的核心技术",
    "深度学习推动了AI的发展"
  ]
}
```

### 批量处理
```json
{
  "model": "gemini-1.5-flash",
  "messages": [
    {
      "role": "user",
      "content": "总结以下文档的要点：[长文档内容]"
    }
  ],
  "temperature": 0.2,
  "max_tokens": 500
}
```

## 📊 PaLM API 测试用例 (传统)

### 文本生成
```json
{
  "model": "text-bison-001",
  "prompt": "写一篇关于可持续发展的文章开头",
  "temperature": 0.7,
  "max_tokens": 200,
  "top_p": 0.8,
  "top_k": 40
}
```

### 聊天模式
```json
{
  "model": "chat-bison-001",
  "messages": [
    {
      "role": "user",
      "content": "你好，我想了解一下气候变化的影响"
    }
  ],
  "temperature": 0.6,
  "max_tokens": 300
}
```

## 🔧 高级配置测试

### 安全过滤
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "请解释核能的工作原理"
    }
  ],
  "safety_settings": [
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    }
  ]
}
```

### 函数调用 (如果支持)
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "今天北京的天气怎么样？"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "获取指定城市的天气信息",
        "parameters": {
          "type": "object",
          "properties": {
            "city": {
              "type": "string",
              "description": "城市名称"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["city"]
        }
      }
    }
  ]
}
```

## 🧪 测试脚本

### Bash测试脚本
```bash
#!/bin/bash

BASE_URL="http://localhost:8080"
API_KEY="your-google-api-key"

echo "Testing Google Gemini API..."

# 测试基础聊天
curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Can you introduce yourself?"
      }
    ],
    "max_tokens": 100
  }' | jq .

# 测试中文对话
curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "你好！请用中文回答：什么是人工智能？"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 200
  }' | jq .

echo "Google API tests completed!"
```

### Python测试脚本
```python
import requests
import json

def test_google_api():
    base_url = "http://localhost:8080"
    api_key = "your-google-api-key"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试Gemini模型
    payload = {
        "model": "gemini-1.5-pro",
        "messages": [
            {
                "role": "user",
                "content": "Explain quantum computing in simple terms"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 300
    }
    
    response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers=headers,
        json=payload
    )
    
    if response.status_code == 200:
        result = response.json()
        print("Success!")
        print(json.dumps(result, indent=2))
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_google_api()
```

## 📝 注意事项

### 1. API限制
- **Gemini API**: 免费层有请求限制
- **Vertex AI**: 按使用量计费
- **PaLM API**: 已弃用，建议迁移到Gemini

### 2. 模型特性
- **gemini-1.5-pro**: 最强性能，支持长上下文
- **gemini-1.5-flash**: 快速响应，成本较低
- **gemini-pro-vision**: 支持图像理解

### 3. 安全设置
- Google API有内置的安全过滤
- 可以通过safety_settings调整过滤级别

### 4. 区域限制
- 某些地区可能无法访问Google AI服务
- Vertex AI需要指定正确的区域

### 5. 认证方式
- AI Studio: API密钥认证
- Vertex AI: 服务账户认证
- 支持环境变量配置
