//! Integration tests for SeaORM database implementation

use litellm_rs::config::DatabaseConfig;
use litellm_rs::core::models::user::{User, UserStatus};
use litellm_rs::storage::database::Database;
use tempfile::tempdir;

#[tokio::test]
async fn test_seaorm_database_basic_operations() {
    // Create temporary database
    let temp_dir = tempdir().unwrap();
    let db_path = temp_dir.path().join("test.db");

    let config = DatabaseConfig {
        url: format!("sqlite://{}?mode=rwc", db_path.display()),
        max_connections: 5,
        connection_timeout: 30,
        enabled: true,
        ssl: false,
    };

    // Initialize database
    let db = Database::new(&config).await.unwrap();

    // Run migrations
    db.migrate().await.unwrap();

    // Test health check
    db.health_check().await.unwrap();

    // Create a test user
    let mut user = User::new(
        "testuser".to_string(),
        "<EMAIL>".to_string(),
        "hashed_password".to_string(),
    );
    user.display_name = Some("Test User".to_string());
    user.status = UserStatus::Active;

    // Test user creation
    let created_user = db.create_user(&user).await.unwrap();
    assert_eq!(created_user.username, user.username);
    assert_eq!(created_user.email, user.email);

    // Test find user by ID
    let found_user = db.find_user_by_id(user.id()).await.unwrap();
    assert!(found_user.is_some());
    let found_user = found_user.unwrap();
    assert_eq!(found_user.username, user.username);

    // Test find user by username
    let found_user = db.find_user_by_username(&user.username).await.unwrap();
    assert!(found_user.is_some());
    let found_user = found_user.unwrap();
    assert_eq!(found_user.email, user.email);

    // Test find user by email
    let found_user = db.find_user_by_email(&user.email).await.unwrap();
    assert!(found_user.is_some());
    let found_user = found_user.unwrap();
    assert_eq!(found_user.username, user.username);

    // Test password update
    db.update_user_password(user.id(), "new_hashed_password")
        .await
        .unwrap();

    // Test email verification
    db.verify_user_email(user.id()).await.unwrap();

    // Test password reset token
    let token = "test_reset_token";
    let expires_at = chrono::Utc::now() + chrono::Duration::hours(1);
    db.store_password_reset_token(user.id(), token, expires_at)
        .await
        .unwrap();

    // Test token verification
    let verified_user_id = db.verify_password_reset_token(token).await.unwrap();
    assert!(verified_user_id.is_some());
    assert_eq!(verified_user_id.unwrap(), user.id());

    // Test token cleanup
    let cleaned_count = db.cleanup_expired_tokens().await.unwrap();
    assert_eq!(cleaned_count, 0); // Token should not be expired yet

    println!("✅ All SeaORM database tests passed!");
}

#[tokio::test]
async fn test_seaorm_batch_operations() {
    // Create temporary database
    let temp_dir = tempdir().unwrap();
    let db_path = temp_dir.path().join("test_batch.db");

    let config = DatabaseConfig {
        url: format!("sqlite://{}?mode=rwc", db_path.display()),
        max_connections: 5,
        connection_timeout: 30,
        enabled: true,
        ssl: false,
    };

    // Initialize database
    let db = Database::new(&config).await.unwrap();

    // Run migrations
    db.migrate().await.unwrap();

    // Create a test batch request
    let batch_request = litellm_rs::core::batch::BatchRequest {
        batch_id: "test_batch_123".to_string(),
        user_id: "user_123".to_string(),
        batch_type: litellm_rs::core::batch::BatchType::ChatCompletion,
        requests: vec![litellm_rs::core::batch::BatchItem {
            custom_id: "req_1".to_string(),
            method: "POST".to_string(),
            url: "/v1/chat/completions".to_string(),
            body: serde_json::json!({"model": "gpt-3.5-turbo", "messages": []}),
        }],
        metadata: std::collections::HashMap::new(),
        completion_window: Some(24),
        webhook_url: None,
    };

    // Test batch creation
    let batch_id = db.create_batch(&batch_request).await.unwrap();
    assert_eq!(batch_id, "test_batch_123");

    // Test batch status update
    db.update_batch_status(&batch_id, "in_progress")
        .await
        .unwrap();

    // Test batch progress update
    db.update_batch_progress(&batch_id, 1, 0).await.unwrap();

    // Test batch completion
    db.mark_batch_completed(&batch_id).await.unwrap();

    // Test batch listing
    let batches = db.list_batches(Some(10), None).await.unwrap();
    assert_eq!(batches.len(), 1);
    assert_eq!(batches[0].id, batch_id);

    println!("✅ All SeaORM batch operation tests passed!");
}
