//! SDK集成测试

use litellm_rs::sdk::*;

#[tokio::test]
async fn test_sdk_basic_functionality() {
    // 创建配置
    let config = ConfigBuilder::new()
        .add_openai("test-openai", "test-key")
        .add_anthropic("test-anthropic", "test-key")
        .default_provider("test-openai")
        .build();

    // 创建客户端
    let client = LLMClient::new(config).unwrap();

    // 测试provider列表
    let providers = client.list_providers();
    assert_eq!(providers.len(), 2);
    assert!(providers.contains(&"test-openai".to_string()));
    assert!(providers.contains(&"test-anthropic".to_string()));

    // 测试聊天功能（模拟响应）
    let messages = vec![Message {
        role: Role::User,
        content: Some(Content::Text("Test message".to_string())),
        name: None,
        tool_calls: None,
    }];

    let response = client.chat(messages).await.unwrap();

    // 验证响应
    assert!(!response.id.is_empty());
    assert_eq!(response.model, "gpt-3.5-turbo");
    assert_eq!(response.choices.len(), 1);
    assert_eq!(response.choices[0].message.role, Role::Assistant);
    assert!(response.usage.total_tokens > 0);
}

#[tokio::test]
async fn test_config_from_env() {
    // 设置环境变量
    unsafe {
        std::env::set_var("OPENAI_API_KEY", "test-openai-key");
    }

    // 从环境变量加载配置
    let config = ClientConfig::from_env().unwrap();

    // 验证配置
    assert_eq!(config.providers.len(), 1);
    assert_eq!(config.providers[0].id, "openai");
    assert_eq!(config.providers[0].api_key, "test-openai-key");

    // 清理环境变量
    unsafe {
        std::env::remove_var("OPENAI_API_KEY");
    }
}

#[test]
fn test_config_builder() {
    let config = ConfigBuilder::new()
        .add_openai("openai-1", "key1")
        .add_anthropic("anthropic-1", "key2")
        .default_provider("openai-1")
        .timeout(60)
        .max_retries(5)
        .build();

    assert_eq!(config.providers.len(), 2);
    assert_eq!(config.default_provider, Some("openai-1".to_string()));
    assert_eq!(config.settings.timeout, 60);
    assert_eq!(config.settings.max_retries, 5);
}

#[test]
fn test_error_handling() {
    // 测试没有provider的配置
    let config = ConfigBuilder::new().build();
    let result = LLMClient::new(config);

    assert!(result.is_err());
    match result.unwrap_err() {
        SDKError::ConfigError(msg) => {
            assert!(msg.contains("No providers configured"));
        }
        _ => panic!("Expected ConfigError"),
    }
}

#[test]
fn test_types_serialization() {
    let message = Message {
        role: Role::User,
        content: Some(Content::Text("Hello".to_string())),
        name: None,
        tool_calls: None,
    };

    // 测试序列化
    let json = serde_json::to_string(&message).unwrap();
    assert!(json.contains("user"));
    assert!(json.contains("Hello"));

    // 测试反序列化
    let deserialized: Message = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.role, Role::User);
    if let Some(Content::Text(text)) = &deserialized.content {
        assert_eq!(text, "Hello");
    } else {
        panic!("Expected text content");
    }
}
